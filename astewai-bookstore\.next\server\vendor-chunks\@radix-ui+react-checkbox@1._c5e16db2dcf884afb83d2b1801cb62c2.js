"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-checkbox@1._c5e16db2dcf884afb83d2b1801cb62c2";
exports.ids = ["vendor-chunks/@radix-ui+react-checkbox@1._c5e16db2dcf884afb83d2b1801cb62c2"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-checkbox@1._c5e16db2dcf884afb83d2b1801cb62c2/node_modules/@radix-ui/react-checkbox/dist/index.mjs":
/*!**********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-checkbox@1._c5e16db2dcf884afb83d2b1801cb62c2/node_modules/@radix-ui/react-checkbox/dist/index.mjs ***!
  \**********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Checkbox: () => (/* binding */ Checkbox),\n/* harmony export */   CheckboxIndicator: () => (/* binding */ CheckboxIndicator),\n/* harmony export */   Indicator: () => (/* binding */ CheckboxIndicator),\n/* harmony export */   Root: () => (/* binding */ Checkbox),\n/* harmony export */   createCheckboxScope: () => (/* binding */ createCheckboxScope),\n/* harmony export */   unstable_BubbleInput: () => (/* binding */ CheckboxBubbleInput),\n/* harmony export */   unstable_CheckboxBubbleInput: () => (/* binding */ CheckboxBubbleInput),\n/* harmony export */   unstable_CheckboxProvider: () => (/* binding */ CheckboxProvider),\n/* harmony export */   unstable_CheckboxTrigger: () => (/* binding */ CheckboxTrigger),\n/* harmony export */   unstable_Provider: () => (/* binding */ CheckboxProvider),\n/* harmony export */   unstable_Trigger: () => (/* binding */ CheckboxTrigger)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.4.4_@babel+core@7.2_0b8959437c64d880cd310af1c435e779/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_9f08440bbab3ef806add91f73ce9eac4/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_ad42a61e498c34b6ab0064ec44eba795/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.2/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-control_c699384c7778101ecedcd597aadb895d/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-previous */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-previou_d028f83ba3caad59e7d80044663957cf/node_modules/@radix-ui/react-use-previous/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-use-size */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-size@1._553827f95b2fad809b215ad51ce61834/node_modules/@radix-ui/react-use-size/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-presence@1._949a0df3eae86665e086aa01aee25ebf/node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_6e0f845fa0b5165e723599b67dc13bbf/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.4_@babel+core@7.2_0b8959437c64d880cd310af1c435e779/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Checkbox,CheckboxIndicator,Indicator,Root,createCheckboxScope,unstable_BubbleInput,unstable_CheckboxBubbleInput,unstable_CheckboxProvider,unstable_CheckboxTrigger,unstable_Provider,unstable_Trigger auto */ // src/checkbox.tsx\n\n\n\n\n\n\n\n\n\n\nvar CHECKBOX_NAME = \"Checkbox\";\nvar [createCheckboxContext, createCheckboxScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(CHECKBOX_NAME);\nvar [CheckboxProviderImpl, useCheckboxContext] = createCheckboxContext(CHECKBOX_NAME);\nfunction CheckboxProvider(props) {\n    const { __scopeCheckbox, checked: checkedProp, children, defaultChecked, disabled, form, name, onCheckedChange, required, value = \"on\", // @ts-expect-error\n    internal_do_not_use_render } = props;\n    const [checked, setChecked] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__.useControllableState)({\n        prop: checkedProp,\n        defaultProp: defaultChecked ?? false,\n        onChange: onCheckedChange,\n        caller: CHECKBOX_NAME\n    });\n    const [control, setControl] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [bubbleInput, setBubbleInput] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const hasConsumerStoppedPropagationRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const isFormControl = control ? !!form || !!control.closest(\"form\") : // We set this to true by default so that events bubble to forms without JS (SSR)\n    true;\n    const context = {\n        checked,\n        disabled,\n        setChecked,\n        control,\n        setControl,\n        name,\n        form,\n        value,\n        hasConsumerStoppedPropagationRef,\n        required,\n        defaultChecked: isIndeterminate(defaultChecked) ? false : defaultChecked,\n        isFormControl,\n        bubbleInput,\n        setBubbleInput\n    };\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CheckboxProviderImpl, {\n        scope: __scopeCheckbox,\n        ...context,\n        children: isFunction(internal_do_not_use_render) ? internal_do_not_use_render(context) : children\n    });\n}\nvar TRIGGER_NAME = \"CheckboxTrigger\";\nvar CheckboxTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(({ __scopeCheckbox, onKeyDown, onClick, ...checkboxProps }, forwardedRef)=>{\n    const { control, value, disabled, checked, required, setControl, setChecked, hasConsumerStoppedPropagationRef, isFormControl, bubbleInput } = useCheckboxContext(TRIGGER_NAME, __scopeCheckbox);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, setControl);\n    const initialCheckedStateRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(checked);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"CheckboxTrigger.useEffect\": ()=>{\n            const form = control?.form;\n            if (form) {\n                const reset = {\n                    \"CheckboxTrigger.useEffect.reset\": ()=>setChecked(initialCheckedStateRef.current)\n                }[\"CheckboxTrigger.useEffect.reset\"];\n                form.addEventListener(\"reset\", reset);\n                return ({\n                    \"CheckboxTrigger.useEffect\": ()=>form.removeEventListener(\"reset\", reset)\n                })[\"CheckboxTrigger.useEffect\"];\n            }\n        }\n    }[\"CheckboxTrigger.useEffect\"], [\n        control,\n        setChecked\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.button, {\n        type: \"button\",\n        role: \"checkbox\",\n        \"aria-checked\": isIndeterminate(checked) ? \"mixed\" : checked,\n        \"aria-required\": required,\n        \"data-state\": getState(checked),\n        \"data-disabled\": disabled ? \"\" : void 0,\n        disabled,\n        value,\n        ...checkboxProps,\n        ref: composedRefs,\n        onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(onKeyDown, (event)=>{\n            if (event.key === \"Enter\") event.preventDefault();\n        }),\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(onClick, (event)=>{\n            setChecked((prevChecked)=>isIndeterminate(prevChecked) ? true : !prevChecked);\n            if (bubbleInput && isFormControl) {\n                hasConsumerStoppedPropagationRef.current = event.isPropagationStopped();\n                if (!hasConsumerStoppedPropagationRef.current) event.stopPropagation();\n            }\n        })\n    });\n});\nCheckboxTrigger.displayName = TRIGGER_NAME;\nvar Checkbox = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeCheckbox, name, checked, defaultChecked, required, disabled, value, onCheckedChange, form, ...checkboxProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CheckboxProvider, {\n        __scopeCheckbox,\n        checked,\n        defaultChecked,\n        disabled,\n        required,\n        onCheckedChange,\n        name,\n        form,\n        value,\n        internal_do_not_use_render: ({ isFormControl })=>/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n                children: [\n                    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CheckboxTrigger, {\n                        ...checkboxProps,\n                        ref: forwardedRef,\n                        __scopeCheckbox\n                    }),\n                    isFormControl && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CheckboxBubbleInput, {\n                        __scopeCheckbox\n                    })\n                ]\n            })\n    });\n});\nCheckbox.displayName = CHECKBOX_NAME;\nvar INDICATOR_NAME = \"CheckboxIndicator\";\nvar CheckboxIndicator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeCheckbox, forceMount, ...indicatorProps } = props;\n    const context = useCheckboxContext(INDICATOR_NAME, __scopeCheckbox);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_7__.Presence, {\n        present: forceMount || isIndeterminate(context.checked) || context.checked === true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.span, {\n            \"data-state\": getState(context.checked),\n            \"data-disabled\": context.disabled ? \"\" : void 0,\n            ...indicatorProps,\n            ref: forwardedRef,\n            style: {\n                pointerEvents: \"none\",\n                ...props.style\n            }\n        })\n    });\n});\nCheckboxIndicator.displayName = INDICATOR_NAME;\nvar BUBBLE_INPUT_NAME = \"CheckboxBubbleInput\";\nvar CheckboxBubbleInput = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(({ __scopeCheckbox, ...props }, forwardedRef)=>{\n    const { control, hasConsumerStoppedPropagationRef, checked, defaultChecked, required, disabled, name, value, form, bubbleInput, setBubbleInput } = useCheckboxContext(BUBBLE_INPUT_NAME, __scopeCheckbox);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, setBubbleInput);\n    const prevChecked = (0,_radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_8__.usePrevious)(checked);\n    const controlSize = (0,_radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_9__.useSize)(control);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"CheckboxBubbleInput.useEffect\": ()=>{\n            const input = bubbleInput;\n            if (!input) return;\n            const inputProto = window.HTMLInputElement.prototype;\n            const descriptor = Object.getOwnPropertyDescriptor(inputProto, \"checked\");\n            const setChecked = descriptor.set;\n            const bubbles = !hasConsumerStoppedPropagationRef.current;\n            if (prevChecked !== checked && setChecked) {\n                const event = new Event(\"click\", {\n                    bubbles\n                });\n                input.indeterminate = isIndeterminate(checked);\n                setChecked.call(input, isIndeterminate(checked) ? false : checked);\n                input.dispatchEvent(event);\n            }\n        }\n    }[\"CheckboxBubbleInput.useEffect\"], [\n        bubbleInput,\n        prevChecked,\n        checked,\n        hasConsumerStoppedPropagationRef\n    ]);\n    const defaultCheckedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(isIndeterminate(checked) ? false : checked);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.input, {\n        type: \"checkbox\",\n        \"aria-hidden\": true,\n        defaultChecked: defaultChecked ?? defaultCheckedRef.current,\n        required,\n        disabled,\n        name,\n        value,\n        form,\n        ...props,\n        tabIndex: -1,\n        ref: composedRefs,\n        style: {\n            ...props.style,\n            ...controlSize,\n            position: \"absolute\",\n            pointerEvents: \"none\",\n            opacity: 0,\n            margin: 0,\n            // We transform because the input is absolutely positioned but we have\n            // rendered it **after** the button. This pulls it back to sit on top\n            // of the button.\n            transform: \"translateX(-100%)\"\n        }\n    });\n});\nCheckboxBubbleInput.displayName = BUBBLE_INPUT_NAME;\nfunction isFunction(value) {\n    return typeof value === \"function\";\n}\nfunction isIndeterminate(checked) {\n    return checked === \"indeterminate\";\n}\nfunction getState(checked) {\n    return isIndeterminate(checked) ? \"indeterminate\" : checked ? \"checked\" : \"unchecked\";\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-checkbox@1._c5e16db2dcf884afb83d2b1801cb62c2/node_modules/@radix-ui/react-checkbox/dist/index.mjs\n");

/***/ })

};
;