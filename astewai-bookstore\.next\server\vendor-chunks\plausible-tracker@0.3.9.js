"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/plausible-tracker@0.3.9";
exports.ids = ["vendor-chunks/plausible-tracker@0.3.9"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/plausible-tracker@0.3.9/node_modules/plausible-tracker/build/module/index.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/plausible-tracker@0.3.9/node_modules/plausible-tracker/build/module/index.js ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _lib_tracker__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/tracker */ \"(ssr)/./node_modules/.pnpm/plausible-tracker@0.3.9/node_modules/plausible-tracker/build/module/lib/tracker.js\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_lib_tracker__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);\n//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW5kZXguanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi9zcmMvaW5kZXgudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsT0FBTyxTQUFTLE1BQU0sZUFBZSxDQUFDO0FBSXRDLGVBQWUsU0FBUyxDQUFDIn0=//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcGxhdXNpYmxlLXRyYWNrZXJAMC4zLjkvbm9kZV9tb2R1bGVzL3BsYXVzaWJsZS10cmFja2VyL2J1aWxkL21vZHVsZS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFzQztBQUN0QyxpRUFBZSxvREFBUyxFQUFDO0FBQ3pCLDJDQUEyQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxwY1xcRGVza3RvcFxcUHJvamVjdHNcXDAwMDFcXGFzdGV3YWktYm9va3N0b3JlXFxub2RlX21vZHVsZXNcXC5wbnBtXFxwbGF1c2libGUtdHJhY2tlckAwLjMuOVxcbm9kZV9tb2R1bGVzXFxwbGF1c2libGUtdHJhY2tlclxcYnVpbGRcXG1vZHVsZVxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFBsYXVzaWJsZSBmcm9tICcuL2xpYi90cmFja2VyJztcbmV4cG9ydCBkZWZhdWx0IFBsYXVzaWJsZTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWRhdGE6YXBwbGljYXRpb24vanNvbjtiYXNlNjQsZXlKMlpYSnphVzl1SWpvekxDSm1hV3hsSWpvaWFXNWtaWGd1YW5NaUxDSnpiM1Z5WTJWU2IyOTBJam9pSWl3aWMyOTFjbU5sY3lJNld5SXVMaTh1TGk5emNtTXZhVzVrWlhndWRITWlYU3dpYm1GdFpYTWlPbHRkTENKdFlYQndhVzVuY3lJNklrRkJRVUVzVDBGQlR5eFRRVUZUTEUxQlFVMHNaVUZCWlN4RFFVRkRPMEZCU1hSRExHVkJRV1VzVTBGQlV5eERRVUZESW4wPSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/plausible-tracker@0.3.9/node_modules/plausible-tracker/build/module/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/plausible-tracker@0.3.9/node_modules/plausible-tracker/build/module/lib/request.js":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/plausible-tracker@0.3.9/node_modules/plausible-tracker/build/module/lib/request.js ***!
  \***************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   sendEvent: () => (/* binding */ sendEvent)\n/* harmony export */ });\n/**\n * @internal\n * Sends an event to Plausible's API\n *\n * @param data - Event data to send\n * @param options - Event options\n */\nfunction sendEvent(eventName, data, options) {\n    const isLocalhost = /^localhost$|^127(?:\\.[0-9]+){0,2}\\.[0-9]+$|^(?:0*:)*?:?0*1$/.test(location.hostname) || location.protocol === 'file:';\n    if (!data.trackLocalhost && isLocalhost) {\n        return console.warn('[Plausible] Ignoring event because website is running locally');\n    }\n    try {\n        if (window.localStorage.plausible_ignore === 'true') {\n            return console.warn('[Plausible] Ignoring event because \"plausible_ignore\" is set to \"true\" in localStorage');\n        }\n    }\n    catch (e) {\n        null;\n    }\n    const payload = {\n        n: eventName,\n        u: data.url,\n        d: data.domain,\n        r: data.referrer,\n        w: data.deviceWidth,\n        h: data.hashMode ? 1 : 0,\n        p: options && options.props ? JSON.stringify(options.props) : undefined,\n    };\n    const req = new XMLHttpRequest();\n    req.open('POST', `${data.apiHost}/api/event`, true);\n    req.setRequestHeader('Content-Type', 'text/plain');\n    req.send(JSON.stringify(payload));\n    // eslint-disable-next-line functional/immutable-data\n    req.onreadystatechange = () => {\n        if (req.readyState !== 4)\n            return;\n        if (options && options.callback) {\n            options.callback();\n        }\n    };\n}\n//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoicmVxdWVzdC5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uL3NyYy9saWIvcmVxdWVzdC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUEyQkE7Ozs7OztHQU1HO0FBQ0gsTUFBTSxVQUFVLFNBQVMsQ0FDdkIsU0FBaUIsRUFDakIsSUFBZ0MsRUFDaEMsT0FBc0I7SUFFdEIsTUFBTSxXQUFXLEdBQ2YsNkRBQTZELENBQUMsSUFBSSxDQUNoRSxRQUFRLENBQUMsUUFBUSxDQUNsQixJQUFJLFFBQVEsQ0FBQyxRQUFRLEtBQUssT0FBTyxDQUFDO0lBRXJDLElBQUksQ0FBQyxJQUFJLENBQUMsY0FBYyxJQUFJLFdBQVcsRUFBRTtRQUN2QyxPQUFPLE9BQU8sQ0FBQyxJQUFJLENBQ2pCLCtEQUErRCxDQUNoRSxDQUFDO0tBQ0g7SUFFRCxJQUFJO1FBQ0YsSUFBSSxNQUFNLENBQUMsWUFBWSxDQUFDLGdCQUFnQixLQUFLLE1BQU0sRUFBRTtZQUNuRCxPQUFPLE9BQU8sQ0FBQyxJQUFJLENBQ2pCLHdGQUF3RixDQUN6RixDQUFDO1NBQ0g7S0FDRjtJQUFDLE9BQU8sQ0FBQyxFQUFFO1FBQ1YsSUFBSSxDQUFDO0tBQ047SUFFRCxNQUFNLE9BQU8sR0FBaUI7UUFDNUIsQ0FBQyxFQUFFLFNBQVM7UUFDWixDQUFDLEVBQUUsSUFBSSxDQUFDLEdBQUc7UUFDWCxDQUFDLEVBQUUsSUFBSSxDQUFDLE1BQU07UUFDZCxDQUFDLEVBQUUsSUFBSSxDQUFDLFFBQVE7UUFDaEIsQ0FBQyxFQUFFLElBQUksQ0FBQyxXQUFXO1FBQ25CLENBQUMsRUFBRSxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDeEIsQ0FBQyxFQUFFLE9BQU8sSUFBSSxPQUFPLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLE9BQU8sQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUMsU0FBUztLQUN4RSxDQUFDO0lBRUYsTUFBTSxHQUFHLEdBQUcsSUFBSSxjQUFjLEVBQUUsQ0FBQztJQUNqQyxHQUFHLENBQUMsSUFBSSxDQUFDLE1BQU0sRUFBRSxHQUFHLElBQUksQ0FBQyxPQUFPLFlBQVksRUFBRSxJQUFJLENBQUMsQ0FBQztJQUNwRCxHQUFHLENBQUMsZ0JBQWdCLENBQUMsY0FBYyxFQUFFLFlBQVksQ0FBQyxDQUFDO0lBQ25ELEdBQUcsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDO0lBQ2xDLHFEQUFxRDtJQUNyRCxHQUFHLENBQUMsa0JBQWtCLEdBQUcsR0FBRyxFQUFFO1FBQzVCLElBQUksR0FBRyxDQUFDLFVBQVUsS0FBSyxDQUFDO1lBQUUsT0FBTztRQUNqQyxJQUFJLE9BQU8sSUFBSSxPQUFPLENBQUMsUUFBUSxFQUFFO1lBQy9CLE9BQU8sQ0FBQyxRQUFRLEVBQUUsQ0FBQztTQUNwQjtJQUNILENBQUMsQ0FBQztBQUNKLENBQUMifQ==//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/plausible-tracker@0.3.9/node_modules/plausible-tracker/build/module/lib/request.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/plausible-tracker@0.3.9/node_modules/plausible-tracker/build/module/lib/tracker.js":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/plausible-tracker@0.3.9/node_modules/plausible-tracker/build/module/lib/tracker.js ***!
  \***************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Plausible)\n/* harmony export */ });\n/* harmony import */ var _request__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./request */ \"(ssr)/./node_modules/.pnpm/plausible-tracker@0.3.9/node_modules/plausible-tracker/build/module/lib/request.js\");\n\n/**\n * Initializes the tracker with your default values.\n *\n * ### Example (es module)\n * ```js\n * import Plausible from 'plausible-tracker'\n *\n * const { enableAutoPageviews, trackEvent } = Plausible({\n *   domain: 'my-app-domain.com',\n *   hashMode: true\n * })\n *\n * enableAutoPageviews()\n *\n * function onUserRegister() {\n *   trackEvent('register')\n * }\n * ```\n *\n * ### Example (commonjs)\n * ```js\n * var Plausible = require('plausible-tracker');\n *\n * var { enableAutoPageviews, trackEvent } = Plausible({\n *   domain: 'my-app-domain.com',\n *   hashMode: true\n * })\n *\n * enableAutoPageviews()\n *\n * function onUserRegister() {\n *   trackEvent('register')\n * }\n * ```\n *\n * @param defaults - Default event parameters that will be applied to all requests.\n */\nfunction Plausible(defaults) {\n    const getConfig = () => ({\n        hashMode: false,\n        trackLocalhost: false,\n        url: location.href,\n        domain: location.hostname,\n        referrer: document.referrer || null,\n        deviceWidth: window.innerWidth,\n        apiHost: 'https://plausible.io',\n        ...defaults,\n    });\n    const trackEvent = (eventName, options, eventData) => {\n        (0,_request__WEBPACK_IMPORTED_MODULE_0__.sendEvent)(eventName, { ...getConfig(), ...eventData }, options);\n    };\n    const trackPageview = (eventData, options) => {\n        trackEvent('pageview', options, eventData);\n    };\n    const enableAutoPageviews = () => {\n        const page = () => trackPageview();\n        // Attach pushState and popState listeners\n        const originalPushState = history.pushState;\n        if (originalPushState) {\n            // eslint-disable-next-line functional/immutable-data\n            history.pushState = function (data, title, url) {\n                originalPushState.apply(this, [data, title, url]);\n                page();\n            };\n            addEventListener('popstate', page);\n        }\n        // Attach hashchange listener\n        if (defaults && defaults.hashMode) {\n            addEventListener('hashchange', page);\n        }\n        // Trigger first page view\n        trackPageview();\n        return function cleanup() {\n            if (originalPushState) {\n                // eslint-disable-next-line functional/immutable-data\n                history.pushState = originalPushState;\n                removeEventListener('popstate', page);\n            }\n            if (defaults && defaults.hashMode) {\n                removeEventListener('hashchange', page);\n            }\n        };\n    };\n    const enableAutoOutboundTracking = (targetNode = document, observerInit = {\n        subtree: true,\n        childList: true,\n        attributes: true,\n        attributeFilter: ['href'],\n    }) => {\n        function trackClick(event) {\n            trackEvent('Outbound Link: Click', { props: { url: this.href } });\n            /* istanbul ignore next */\n            // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n            // @ts-ignore\n            if (!(typeof process !== 'undefined' &&\n                process &&\n                \"development\" === 'test')) {\n                setTimeout(() => {\n                    // eslint-disable-next-line functional/immutable-data\n                    location.href = this.href;\n                }, 150);\n            }\n            event.preventDefault();\n        }\n        // eslint-disable-next-line functional/prefer-readonly-type\n        const tracked = new Set();\n        function addNode(node) {\n            if (node instanceof HTMLAnchorElement) {\n                if (node.host !== location.host) {\n                    node.addEventListener('click', trackClick);\n                    tracked.add(node);\n                }\n            } /* istanbul ignore next */\n            else if ('querySelectorAll' in node) {\n                node.querySelectorAll('a').forEach(addNode);\n            }\n        }\n        function removeNode(node) {\n            if (node instanceof HTMLAnchorElement) {\n                node.removeEventListener('click', trackClick);\n                tracked.delete(node);\n            } /* istanbul ignore next */\n            else if ('querySelectorAll' in node) {\n                node.querySelectorAll('a').forEach(removeNode);\n            }\n        }\n        const observer = new MutationObserver((mutations) => {\n            mutations.forEach((mutation) => {\n                if (mutation.type === 'attributes') {\n                    // Handle changed href\n                    removeNode(mutation.target);\n                    addNode(mutation.target);\n                } /* istanbul ignore next */\n                else if (mutation.type === 'childList') {\n                    // Handle added nodes\n                    mutation.addedNodes.forEach(addNode);\n                    // Handle removed nodes\n                    mutation.removedNodes.forEach(removeNode);\n                }\n            });\n        });\n        // Track existing nodes\n        targetNode.querySelectorAll('a').forEach(addNode);\n        // Observe mutations\n        observer.observe(targetNode, observerInit);\n        return function cleanup() {\n            tracked.forEach((a) => {\n                a.removeEventListener('click', trackClick);\n            });\n            tracked.clear();\n            observer.disconnect();\n        };\n    };\n    return {\n        trackEvent,\n        trackPageview,\n        enableAutoPageviews,\n        enableAutoOutboundTracking,\n    };\n}\n//# sourceMappingURL=data:application/json;base64,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# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/plausible-tracker@0.3.9/node_modules/plausible-tracker/build/module/lib/tracker.js\n");

/***/ })

};
;