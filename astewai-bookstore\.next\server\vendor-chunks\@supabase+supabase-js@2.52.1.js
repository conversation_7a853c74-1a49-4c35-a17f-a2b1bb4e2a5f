"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@supabase+supabase-js@2.52.1";
exports.ids = ["vendor-chunks/@supabase+supabase-js@2.52.1"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/SupabaseClient.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/SupabaseClient.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SupabaseClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_functions_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @supabase/functions-js */ \"(rsc)/./node_modules/.pnpm/@supabase+functions-js@2.4.5/node_modules/@supabase/functions-js/dist/module/FunctionsClient.js\");\n/* harmony import */ var _supabase_postgrest_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/postgrest-js */ \"(rsc)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs\");\n/* harmony import */ var _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/realtime-js */ \"(rsc)/./node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/index.js\");\n/* harmony import */ var _supabase_storage_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @supabase/storage-js */ \"(rsc)/./node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/StorageClient.js\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/constants */ \"(rsc)/./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/lib/constants.js\");\n/* harmony import */ var _lib_fetch__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lib/fetch */ \"(rsc)/./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/lib/fetch.js\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/helpers */ \"(rsc)/./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/lib/helpers.js\");\n/* harmony import */ var _lib_SupabaseAuthClient__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./lib/SupabaseAuthClient */ \"(rsc)/./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/lib/SupabaseAuthClient.js\");\nvar __awaiter = undefined && undefined.__awaiter || function(thisArg, _arguments, P, generator) {\n    function adopt(value) {\n        return value instanceof P ? value : new P(function(resolve) {\n            resolve(value);\n        });\n    }\n    return new (P || (P = Promise))(function(resolve, reject) {\n        function fulfilled(value) {\n            try {\n                step(generator.next(value));\n            } catch (e) {\n                reject(e);\n            }\n        }\n        function rejected(value) {\n            try {\n                step(generator[\"throw\"](value));\n            } catch (e) {\n                reject(e);\n            }\n        }\n        function step(result) {\n            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n        }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n\n\n\n\n\n\n\n\n/**\n * Supabase Client.\n *\n * An isomorphic Javascript client for interacting with Postgres.\n */ class SupabaseClient {\n    /**\n     * Create a new client for use in the browser.\n     * @param supabaseUrl The unique Supabase URL which is supplied when you create a new project in your project dashboard.\n     * @param supabaseKey The unique Supabase Key which is supplied when you create a new project in your project dashboard.\n     * @param options.db.schema You can switch in between schemas. The schema needs to be on the list of exposed schemas inside Supabase.\n     * @param options.auth.autoRefreshToken Set to \"true\" if you want to automatically refresh the token before expiring.\n     * @param options.auth.persistSession Set to \"true\" if you want to automatically save the user session into local storage.\n     * @param options.auth.detectSessionInUrl Set to \"true\" if you want to automatically detects OAuth grants in the URL and signs in the user.\n     * @param options.realtime Options passed along to realtime-js constructor.\n     * @param options.global.fetch A custom fetch implementation.\n     * @param options.global.headers Any additional headers to send with each network request.\n     */ constructor(supabaseUrl, supabaseKey, options){\n        var _a, _b, _c;\n        this.supabaseUrl = supabaseUrl;\n        this.supabaseKey = supabaseKey;\n        if (!supabaseUrl) throw new Error('supabaseUrl is required.');\n        if (!supabaseKey) throw new Error('supabaseKey is required.');\n        const _supabaseUrl = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_2__.ensureTrailingSlash)(supabaseUrl);\n        const baseUrl = new URL(_supabaseUrl);\n        this.realtimeUrl = new URL('realtime/v1', baseUrl);\n        this.realtimeUrl.protocol = this.realtimeUrl.protocol.replace('http', 'ws');\n        this.authUrl = new URL('auth/v1', baseUrl);\n        this.storageUrl = new URL('storage/v1', baseUrl);\n        this.functionsUrl = new URL('functions/v1', baseUrl);\n        // default storage key uses the supabase project ref as a namespace\n        const defaultStorageKey = `sb-${baseUrl.hostname.split('.')[0]}-auth-token`;\n        const DEFAULTS = {\n            db: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_DB_OPTIONS,\n            realtime: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_REALTIME_OPTIONS,\n            auth: Object.assign(Object.assign({}, _lib_constants__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_AUTH_OPTIONS), {\n                storageKey: defaultStorageKey\n            }),\n            global: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_GLOBAL_OPTIONS\n        };\n        const settings = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_2__.applySettingDefaults)(options !== null && options !== void 0 ? options : {}, DEFAULTS);\n        this.storageKey = (_a = settings.auth.storageKey) !== null && _a !== void 0 ? _a : '';\n        this.headers = (_b = settings.global.headers) !== null && _b !== void 0 ? _b : {};\n        if (!settings.accessToken) {\n            this.auth = this._initSupabaseAuthClient((_c = settings.auth) !== null && _c !== void 0 ? _c : {}, this.headers, settings.global.fetch);\n        } else {\n            this.accessToken = settings.accessToken;\n            this.auth = new Proxy({}, {\n                get: (_, prop)=>{\n                    throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(prop)} is not possible`);\n                }\n            });\n        }\n        this.fetch = (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_4__.fetchWithAuth)(supabaseKey, this._getAccessToken.bind(this), settings.global.fetch);\n        this.realtime = this._initRealtimeClient(Object.assign({\n            headers: this.headers,\n            accessToken: this._getAccessToken.bind(this)\n        }, settings.realtime));\n        this.rest = new _supabase_postgrest_js__WEBPACK_IMPORTED_MODULE_0__.PostgrestClient(new URL('rest/v1', baseUrl).href, {\n            headers: this.headers,\n            schema: settings.db.schema,\n            fetch: this.fetch\n        });\n        if (!settings.accessToken) {\n            this._listenForAuthEvents();\n        }\n    }\n    /**\n     * Supabase Functions allows you to deploy and invoke edge functions.\n     */ get functions() {\n        return new _supabase_functions_js__WEBPACK_IMPORTED_MODULE_5__.FunctionsClient(this.functionsUrl.href, {\n            headers: this.headers,\n            customFetch: this.fetch\n        });\n    }\n    /**\n     * Supabase Storage allows you to manage user-generated content, such as photos or videos.\n     */ get storage() {\n        return new _supabase_storage_js__WEBPACK_IMPORTED_MODULE_6__.StorageClient(this.storageUrl.href, this.headers, this.fetch);\n    }\n    /**\n     * Perform a query on a table or a view.\n     *\n     * @param relation - The table or view name to query\n     */ from(relation) {\n        return this.rest.from(relation);\n    }\n    // NOTE: signatures must be kept in sync with PostgrestClient.schema\n    /**\n     * Select a schema to query or perform an function (rpc) call.\n     *\n     * The schema needs to be on the list of exposed schemas inside Supabase.\n     *\n     * @param schema - The schema to query\n     */ schema(schema) {\n        return this.rest.schema(schema);\n    }\n    // NOTE: signatures must be kept in sync with PostgrestClient.rpc\n    /**\n     * Perform a function call.\n     *\n     * @param fn - The function name to call\n     * @param args - The arguments to pass to the function call\n     * @param options - Named parameters\n     * @param options.head - When set to `true`, `data` will not be returned.\n     * Useful if you only need the count.\n     * @param options.get - When set to `true`, the function will be called with\n     * read-only access mode.\n     * @param options.count - Count algorithm to use to count rows returned by the\n     * function. Only applicable for [set-returning\n     * functions](https://www.postgresql.org/docs/current/functions-srf.html).\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     */ rpc(fn, args = {}, options = {}) {\n        return this.rest.rpc(fn, args, options);\n    }\n    /**\n     * Creates a Realtime channel with Broadcast, Presence, and Postgres Changes.\n     *\n     * @param {string} name - The name of the Realtime channel.\n     * @param {Object} opts - The options to pass to the Realtime channel.\n     *\n     */ channel(name, opts = {\n        config: {}\n    }) {\n        return this.realtime.channel(name, opts);\n    }\n    /**\n     * Returns all Realtime channels.\n     */ getChannels() {\n        return this.realtime.getChannels();\n    }\n    /**\n     * Unsubscribes and removes Realtime channel from Realtime client.\n     *\n     * @param {RealtimeChannel} channel - The name of the Realtime channel.\n     *\n     */ removeChannel(channel) {\n        return this.realtime.removeChannel(channel);\n    }\n    /**\n     * Unsubscribes and removes all Realtime channels from Realtime client.\n     */ removeAllChannels() {\n        return this.realtime.removeAllChannels();\n    }\n    _getAccessToken() {\n        var _a, _b;\n        return __awaiter(this, void 0, void 0, function*() {\n            if (this.accessToken) {\n                return yield this.accessToken();\n            }\n            const { data } = yield this.auth.getSession();\n            return (_b = (_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token) !== null && _b !== void 0 ? _b : null;\n        });\n    }\n    _initSupabaseAuthClient({ autoRefreshToken, persistSession, detectSessionInUrl, storage, storageKey, flowType, lock, debug }, headers, fetch) {\n        const authHeaders = {\n            Authorization: `Bearer ${this.supabaseKey}`,\n            apikey: `${this.supabaseKey}`\n        };\n        return new _lib_SupabaseAuthClient__WEBPACK_IMPORTED_MODULE_7__.SupabaseAuthClient({\n            url: this.authUrl.href,\n            headers: Object.assign(Object.assign({}, authHeaders), headers),\n            storageKey: storageKey,\n            autoRefreshToken,\n            persistSession,\n            detectSessionInUrl,\n            storage,\n            flowType,\n            lock,\n            debug,\n            fetch,\n            // auth checks if there is a custom authorizaiton header using this flag\n            // so it knows whether to return an error when getUser is called with no session\n            hasCustomAuthorizationHeader: 'Authorization' in this.headers\n        });\n    }\n    _initRealtimeClient(options) {\n        return new _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_1__.RealtimeClient(this.realtimeUrl.href, Object.assign(Object.assign({}, options), {\n            params: Object.assign({\n                apikey: this.supabaseKey\n            }, options === null || options === void 0 ? void 0 : options.params)\n        }));\n    }\n    _listenForAuthEvents() {\n        let data = this.auth.onAuthStateChange((event, session)=>{\n            this._handleTokenChanged(event, 'CLIENT', session === null || session === void 0 ? void 0 : session.access_token);\n        });\n        return data;\n    }\n    _handleTokenChanged(event, source, token) {\n        if ((event === 'TOKEN_REFRESHED' || event === 'SIGNED_IN') && this.changedAccessToken !== token) {\n            this.changedAccessToken = token;\n        } else if (event === 'SIGNED_OUT') {\n            this.realtime.setAuth();\n            if (source == 'STORAGE') this.auth.signOut();\n            this.changedAccessToken = undefined;\n        }\n    }\n} //# sourceMappingURL=SupabaseClient.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/SupabaseClient.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/index.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/index.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthAdminApi: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthAdminApi),\n/* harmony export */   AuthApiError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthApiError),\n/* harmony export */   AuthClient: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthClient),\n/* harmony export */   AuthError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthError),\n/* harmony export */   AuthImplicitGrantRedirectError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthImplicitGrantRedirectError),\n/* harmony export */   AuthInvalidCredentialsError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthInvalidCredentialsError),\n/* harmony export */   AuthInvalidJwtError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthInvalidJwtError),\n/* harmony export */   AuthInvalidTokenResponseError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthInvalidTokenResponseError),\n/* harmony export */   AuthPKCEGrantCodeExchangeError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthPKCEGrantCodeExchangeError),\n/* harmony export */   AuthRetryableFetchError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthRetryableFetchError),\n/* harmony export */   AuthSessionMissingError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthSessionMissingError),\n/* harmony export */   AuthUnknownError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthUnknownError),\n/* harmony export */   AuthWeakPasswordError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthWeakPasswordError),\n/* harmony export */   CustomAuthError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.CustomAuthError),\n/* harmony export */   FunctionRegion: () => (/* reexport safe */ _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__.FunctionRegion),\n/* harmony export */   FunctionsError: () => (/* reexport safe */ _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__.FunctionsError),\n/* harmony export */   FunctionsFetchError: () => (/* reexport safe */ _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__.FunctionsFetchError),\n/* harmony export */   FunctionsHttpError: () => (/* reexport safe */ _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__.FunctionsHttpError),\n/* harmony export */   FunctionsRelayError: () => (/* reexport safe */ _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__.FunctionsRelayError),\n/* harmony export */   GoTrueAdminApi: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.GoTrueAdminApi),\n/* harmony export */   GoTrueClient: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.GoTrueClient),\n/* harmony export */   NavigatorLockAcquireTimeoutError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.NavigatorLockAcquireTimeoutError),\n/* harmony export */   PostgrestError: () => (/* reexport safe */ _supabase_postgrest_js__WEBPACK_IMPORTED_MODULE_1__.PostgrestError),\n/* harmony export */   REALTIME_CHANNEL_STATES: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.REALTIME_CHANNEL_STATES),\n/* harmony export */   REALTIME_LISTEN_TYPES: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.REALTIME_LISTEN_TYPES),\n/* harmony export */   REALTIME_POSTGRES_CHANGES_LISTEN_EVENT: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.REALTIME_POSTGRES_CHANGES_LISTEN_EVENT),\n/* harmony export */   REALTIME_PRESENCE_LISTEN_EVENTS: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.REALTIME_PRESENCE_LISTEN_EVENTS),\n/* harmony export */   REALTIME_SUBSCRIBE_STATES: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.REALTIME_SUBSCRIBE_STATES),\n/* harmony export */   RealtimeChannel: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.RealtimeChannel),\n/* harmony export */   RealtimeClient: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.RealtimeClient),\n/* harmony export */   RealtimePresence: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.RealtimePresence),\n/* harmony export */   SIGN_OUT_SCOPES: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.SIGN_OUT_SCOPES),\n/* harmony export */   SupabaseClient: () => (/* reexport safe */ _SupabaseClient__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   createClient: () => (/* binding */ createClient),\n/* harmony export */   isAuthApiError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthApiError),\n/* harmony export */   isAuthError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthError),\n/* harmony export */   isAuthImplicitGrantRedirectError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthImplicitGrantRedirectError),\n/* harmony export */   isAuthRetryableFetchError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthRetryableFetchError),\n/* harmony export */   isAuthSessionMissingError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthSessionMissingError),\n/* harmony export */   isAuthWeakPasswordError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthWeakPasswordError),\n/* harmony export */   lockInternals: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.lockInternals),\n/* harmony export */   navigatorLock: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.navigatorLock),\n/* harmony export */   processLock: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.processLock)\n/* harmony export */ });\n/* harmony import */ var _SupabaseClient__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./SupabaseClient */ \"(rsc)/./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/SupabaseClient.js\");\n/* harmony import */ var _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-js */ \"(rsc)/./node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/index.js\");\n/* harmony import */ var _supabase_postgrest_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/postgrest-js */ \"(rsc)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs\");\n/* harmony import */ var _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/functions-js */ \"(rsc)/./node_modules/.pnpm/@supabase+functions-js@2.4.5/node_modules/@supabase/functions-js/dist/module/types.js\");\n/* harmony import */ var _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @supabase/realtime-js */ \"(rsc)/./node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/index.js\");\n\n\n\n\n\n\n/**\n * Creates a new Supabase Client.\n */ const createClient = (supabaseUrl, supabaseKey, options)=>{\n    return new _SupabaseClient__WEBPACK_IMPORTED_MODULE_4__[\"default\"](supabaseUrl, supabaseKey, options);\n};\n// Check for Node.js <= 18 deprecation\nfunction shouldShowDeprecationWarning() {\n    if ( false || typeof process === 'undefined' || process.version === undefined || process.version === null) {\n        return false;\n    }\n    const versionMatch = process.version.match(/^v(\\d+)\\./);\n    if (!versionMatch) {\n        return false;\n    }\n    const majorVersion = parseInt(versionMatch[1], 10);\n    return majorVersion <= 18;\n}\nif (shouldShowDeprecationWarning()) {\n    console.warn(`⚠️  Node.js 18 and below are deprecated and will no longer be supported in future versions of @supabase/supabase-js. ` + `Please upgrade to Node.js 20 or later. ` + `For more information, visit: https://github.com/orgs/supabase/discussions/37217`);\n} //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTIuMS9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3N1cGFiYXNlLWpzL2Rpc3QvbW9kdWxlL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUE2QztBQUdaO0FBT0Y7QUFRQTtBQUNNO0FBQ3VCO0FBRzVEOztHQUVHLENBQ0ksTUFBTSxZQUFZLEdBQUcsQ0FTMUIsV0FBbUIsRUFDbkIsV0FBbUIsRUFDbkIsT0FBMkMsRUFDRyxFQUFFO0lBQ2hELE9BQU8sSUFBSSx1REFBYyxDQUErQixXQUFXLEVBQUUsV0FBVyxFQUFFLE9BQU8sQ0FBQztBQUM1RixDQUFDO0FBRUQsc0NBQXNDO0FBQ3RDLFNBQVMsNEJBQTRCO0lBQ25DLElBQ0UsTUFBNkIsSUFDN0IsT0FBTyxPQUFPLEtBQUssV0FBVyxJQUM5QixPQUFPLENBQUMsT0FBTyxLQUFLLFNBQVMsSUFDN0IsT0FBTyxDQUFDLE9BQU8sS0FBSyxJQUFJLEVBQ3hCO1FBQ0EsT0FBTyxLQUFLO0tBQ2I7SUFFRCxNQUFNLFlBQVksR0FBRyxPQUFPLENBQUMsT0FBTyxDQUFDLEtBQUssQ0FBQyxXQUFXLENBQUM7SUFDdkQsSUFBSSxDQUFDLFlBQVksRUFBRTtRQUNqQixPQUFPLEtBQUs7S0FDYjtJQUVELE1BQU0sWUFBWSxHQUFHLFFBQVEsQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLEVBQUUsRUFBRSxDQUFDO0lBQ2xELE9BQU8sWUFBWSxJQUFJLEVBQUU7QUFDM0IsQ0FBQztBQUVELElBQUksNEJBQTRCLEVBQUUsRUFBRTtJQUNsQyxPQUFPLENBQUMsSUFBSSxDQUNWLHVIQUF1SCxHQUNySCx5Q0FBeUMsR0FDekMsaUZBQWlGLENBQ3BGO0NBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccGNcXERlc2t0b3BcXFByb2plY3RzXFxzcmNcXGluZGV4LnRzIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/lib/SupabaseAuthClient.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/lib/SupabaseAuthClient.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupabaseAuthClient: () => (/* binding */ SupabaseAuthClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-js */ \"(rsc)/./node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/index.js\");\n\nclass SupabaseAuthClient extends _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthClient {\n    constructor(options){\n        super(options);\n    }\n} //# sourceMappingURL=SupabaseAuthClient.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTIuMS9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3N1cGFiYXNlLWpzL2Rpc3QvbW9kdWxlL2xpYi9TdXBhYmFzZUF1dGhDbGllbnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBOEM7QUFHeEMsTUFBTyxrQkFBbUIsU0FBUSx5REFBVTtJQUNoRCxZQUFZLE9BQWtDO1FBQzVDLEtBQUssQ0FBQyxPQUFPLENBQUM7SUFDaEIsQ0FBQztDQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHBjXFxEZXNrdG9wXFxzcmNcXGxpYlxcU3VwYWJhc2VBdXRoQ2xpZW50LnRzIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/lib/SupabaseAuthClient.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/lib/constants.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/lib/constants.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_AUTH_OPTIONS: () => (/* binding */ DEFAULT_AUTH_OPTIONS),\n/* harmony export */   DEFAULT_DB_OPTIONS: () => (/* binding */ DEFAULT_DB_OPTIONS),\n/* harmony export */   DEFAULT_GLOBAL_OPTIONS: () => (/* binding */ DEFAULT_GLOBAL_OPTIONS),\n/* harmony export */   DEFAULT_HEADERS: () => (/* binding */ DEFAULT_HEADERS),\n/* harmony export */   DEFAULT_REALTIME_OPTIONS: () => (/* binding */ DEFAULT_REALTIME_OPTIONS)\n/* harmony export */ });\n/* harmony import */ var _version__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./version */ \"(rsc)/./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/lib/version.js\");\n\nlet JS_ENV = '';\n// @ts-ignore\nif (typeof Deno !== 'undefined') {\n    JS_ENV = 'deno';\n} else if (typeof document !== 'undefined') {\n    JS_ENV = 'web';\n} else if (typeof navigator !== 'undefined' && navigator.product === 'ReactNative') {\n    JS_ENV = 'react-native';\n} else {\n    JS_ENV = 'node';\n}\nconst DEFAULT_HEADERS = {\n    'X-Client-Info': `supabase-js-${JS_ENV}/${_version__WEBPACK_IMPORTED_MODULE_0__.version}`\n};\nconst DEFAULT_GLOBAL_OPTIONS = {\n    headers: DEFAULT_HEADERS\n};\nconst DEFAULT_DB_OPTIONS = {\n    schema: 'public'\n};\nconst DEFAULT_AUTH_OPTIONS = {\n    autoRefreshToken: true,\n    persistSession: true,\n    detectSessionInUrl: true,\n    flowType: 'implicit'\n};\nconst DEFAULT_REALTIME_OPTIONS = {}; //# sourceMappingURL=constants.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTIuMS9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3N1cGFiYXNlLWpzL2Rpc3QvbW9kdWxlL2xpYi9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBR21DO0FBRW5DLElBQUksTUFBTSxHQUFHLEVBQUU7QUFDZixhQUFhO0FBQ2IsSUFBSSxPQUFPLElBQUksS0FBSyxXQUFXLEVBQUU7SUFDL0IsTUFBTSxHQUFHLE1BQU07Q0FDaEIsTUFBTSxJQUFJLE9BQU8sUUFBUSxLQUFLLFdBQVcsRUFBRTtJQUMxQyxNQUFNLEdBQUcsS0FBSztDQUNmLE1BQU0sSUFBSSxPQUFPLFNBQVMsS0FBSyxXQUFXLElBQUksU0FBUyxDQUFDLE9BQU8sS0FBSyxhQUFhLEVBQUU7SUFDbEYsTUFBTSxHQUFHLGNBQWM7Q0FDeEIsTUFBTTtJQUNMLE1BQU0sR0FBRyxNQUFNO0NBQ2hCO0FBRU0sTUFBTSxlQUFlLEdBQUc7SUFBRSxlQUFlLEVBQUUsZUFBZSxNQUFNLElBQUksNkNBQU8sRUFBRTtBQUFBLENBQUU7QUFFL0UsTUFBTSxzQkFBc0IsR0FBRztJQUNwQyxPQUFPLEVBQUUsZUFBZTtDQUN6QjtBQUVNLE1BQU0sa0JBQWtCLEdBQUc7SUFDaEMsTUFBTSxFQUFFLFFBQVE7Q0FDakI7QUFFTSxNQUFNLG9CQUFvQixHQUE4QjtJQUM3RCxnQkFBZ0IsRUFBRSxJQUFJO0lBQ3RCLGNBQWMsRUFBRSxJQUFJO0lBQ3BCLGtCQUFrQixFQUFFLElBQUk7SUFDeEIsUUFBUSxFQUFFLFVBQVU7Q0FDckI7QUFFTSxNQUFNLHdCQUF3QixHQUEwQixFQUFFIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHBjXFxEZXNrdG9wXFxzcmNcXGxpYlxcY29uc3RhbnRzLnRzIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/lib/constants.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/lib/fetch.js":
/*!*********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/lib/fetch.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fetchWithAuth: () => (/* binding */ fetchWithAuth),\n/* harmony export */   resolveFetch: () => (/* binding */ resolveFetch),\n/* harmony export */   resolveHeadersConstructor: () => (/* binding */ resolveHeadersConstructor)\n/* harmony export */ });\n/* harmony import */ var _supabase_node_fetch__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/node-fetch */ \"(rsc)/./node_modules/.pnpm/@supabase+node-fetch@2.6.15/node_modules/@supabase/node-fetch/lib/index.js\");\n/* harmony import */ var _supabase_node_fetch__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_node_fetch__WEBPACK_IMPORTED_MODULE_0__);\nvar __awaiter = undefined && undefined.__awaiter || function(thisArg, _arguments, P, generator) {\n    function adopt(value) {\n        return value instanceof P ? value : new P(function(resolve) {\n            resolve(value);\n        });\n    }\n    return new (P || (P = Promise))(function(resolve, reject) {\n        function fulfilled(value) {\n            try {\n                step(generator.next(value));\n            } catch (e) {\n                reject(e);\n            }\n        }\n        function rejected(value) {\n            try {\n                step(generator[\"throw\"](value));\n            } catch (e) {\n                reject(e);\n            }\n        }\n        function step(result) {\n            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n        }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n// @ts-ignore\n\nconst resolveFetch = (customFetch)=>{\n    let _fetch;\n    if (customFetch) {\n        _fetch = customFetch;\n    } else if (typeof fetch === 'undefined') {\n        _fetch = (_supabase_node_fetch__WEBPACK_IMPORTED_MODULE_0___default());\n    } else {\n        _fetch = fetch;\n    }\n    return (...args)=>_fetch(...args);\n};\nconst resolveHeadersConstructor = ()=>{\n    if (typeof Headers === 'undefined') {\n        return _supabase_node_fetch__WEBPACK_IMPORTED_MODULE_0__.Headers;\n    }\n    return Headers;\n};\nconst fetchWithAuth = (supabaseKey, getAccessToken, customFetch)=>{\n    const fetch1 = resolveFetch(customFetch);\n    const HeadersConstructor = resolveHeadersConstructor();\n    return (input, init)=>__awaiter(void 0, void 0, void 0, function*() {\n            var _a;\n            const accessToken = (_a = yield getAccessToken()) !== null && _a !== void 0 ? _a : supabaseKey;\n            let headers = new HeadersConstructor(init === null || init === void 0 ? void 0 : init.headers);\n            if (!headers.has('apikey')) {\n                headers.set('apikey', supabaseKey);\n            }\n            if (!headers.has('Authorization')) {\n                headers.set('Authorization', `Bearer ${accessToken}`);\n            }\n            return fetch1(input, Object.assign(Object.assign({}, init), {\n                headers\n            }));\n        });\n}; //# sourceMappingURL=fetch.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTIuMS9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3N1cGFiYXNlLWpzL2Rpc3QvbW9kdWxlL2xpYi9mZXRjaC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLGFBQWE7QUFDZ0U7QUFJdEUsTUFBTSxZQUFZLEdBQUcsQ0FBQyxXQUFtQixFQUFTLEVBQUU7SUFDekQsSUFBSSxNQUFhO0lBQ2pCLElBQUksV0FBVyxFQUFFO1FBQ2YsTUFBTSxHQUFHLFdBQVc7S0FDckIsTUFBTSxJQUFJLE9BQU8sS0FBSyxLQUFLLFdBQVcsRUFBRTtRQUN2QyxNQUFNLEdBQUcsNkRBQTZCO0tBQ3ZDLE1BQU07UUFDTCxNQUFNLEdBQUcsS0FBSztLQUNmO0lBQ0QsT0FBTyxDQUFDLEdBQUcsSUFBdUIsRUFBRSxDQUFHLENBQUQsS0FBTyxDQUFDLEdBQUcsSUFBSSxDQUFDO0FBQ3hELENBQUM7QUFFTSxNQUFNLHlCQUF5QixHQUFHLEdBQUcsRUFBRTtJQUM1QyxJQUFJLE9BQU8sT0FBTyxLQUFLLFdBQVcsRUFBRTtRQUNsQyxPQUFPLHlEQUFnQjtLQUN4QjtJQUVELE9BQU8sT0FBTztBQUNoQixDQUFDO0FBRU0sTUFBTSxhQUFhLEdBQUcsQ0FDM0IsV0FBbUIsRUFDbkIsY0FBNEMsRUFDNUMsV0FBbUIsRUFDWixFQUFFO0lBQ1QsTUFBTSxLQUFLLElBQUcsWUFBWSxDQUFDLFdBQVcsQ0FBQztJQUN2QyxNQUFNLGtCQUFrQixHQUFHLHlCQUF5QixFQUFFO0lBRXRELE9BQU8sQ0FBTyxLQUFLLEVBQUUsSUFBSSxFQUFFLENBQUU7O1lBQzNCLE1BQU0sV0FBVyxHQUFHLFlBQU8sY0FBYyxFQUFFLENBQUMsa0NBQUksV0FBVztZQUMzRCxJQUFJLE9BQU8sR0FBRyxJQUFJLGtCQUFrQixDQUFDLElBQUksYUFBSixJQUFJLHVCQUFKLElBQUksQ0FBRSxPQUFPLENBQUM7WUFFbkQsSUFBSSxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsUUFBUSxDQUFDLEVBQUU7Z0JBQzFCLE9BQU8sQ0FBQyxHQUFHLENBQUMsUUFBUSxFQUFFLFdBQVcsQ0FBQzthQUNuQztZQUVELElBQUksQ0FBQyxPQUFPLENBQUMsR0FBRyxDQUFDLGVBQWUsQ0FBQyxFQUFFO2dCQUNqQyxPQUFPLENBQUMsR0FBRyxDQUFDLGVBQWUsRUFBRSxVQUFVLFdBQVcsRUFBRSxDQUFDO2FBQ3REO1lBRUQsT0FBTyxLQUFLLEVBQUMsS0FBSyxrQ0FBTyxJQUFJO2dCQUFFLE9BQU87WUFBQSxHQUFHO1FBQzNDLENBQUM7QUFDSCxDQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHBjXFxEZXNrdG9wXFxzcmNcXGxpYlxcZmV0Y2gudHMiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/lib/fetch.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/lib/helpers.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/lib/helpers.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applySettingDefaults: () => (/* binding */ applySettingDefaults),\n/* harmony export */   ensureTrailingSlash: () => (/* binding */ ensureTrailingSlash),\n/* harmony export */   isBrowser: () => (/* binding */ isBrowser),\n/* harmony export */   uuid: () => (/* binding */ uuid)\n/* harmony export */ });\nvar __awaiter = undefined && undefined.__awaiter || function(thisArg, _arguments, P, generator) {\n    function adopt(value) {\n        return value instanceof P ? value : new P(function(resolve) {\n            resolve(value);\n        });\n    }\n    return new (P || (P = Promise))(function(resolve, reject) {\n        function fulfilled(value) {\n            try {\n                step(generator.next(value));\n            } catch (e) {\n                reject(e);\n            }\n        }\n        function rejected(value) {\n            try {\n                step(generator[\"throw\"](value));\n            } catch (e) {\n                reject(e);\n            }\n        }\n        function step(result) {\n            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n        }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nfunction uuid() {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\n        var r = Math.random() * 16 | 0, v = c == 'x' ? r : r & 0x3 | 0x8;\n        return v.toString(16);\n    });\n}\nfunction ensureTrailingSlash(url) {\n    return url.endsWith('/') ? url : url + '/';\n}\nconst isBrowser = ()=>\"undefined\" !== 'undefined';\nfunction applySettingDefaults(options, defaults) {\n    var _a, _b;\n    const { db: dbOptions, auth: authOptions, realtime: realtimeOptions, global: globalOptions } = options;\n    const { db: DEFAULT_DB_OPTIONS, auth: DEFAULT_AUTH_OPTIONS, realtime: DEFAULT_REALTIME_OPTIONS, global: DEFAULT_GLOBAL_OPTIONS } = defaults;\n    const result = {\n        db: Object.assign(Object.assign({}, DEFAULT_DB_OPTIONS), dbOptions),\n        auth: Object.assign(Object.assign({}, DEFAULT_AUTH_OPTIONS), authOptions),\n        realtime: Object.assign(Object.assign({}, DEFAULT_REALTIME_OPTIONS), realtimeOptions),\n        global: Object.assign(Object.assign(Object.assign({}, DEFAULT_GLOBAL_OPTIONS), globalOptions), {\n            headers: Object.assign(Object.assign({}, (_a = DEFAULT_GLOBAL_OPTIONS === null || DEFAULT_GLOBAL_OPTIONS === void 0 ? void 0 : DEFAULT_GLOBAL_OPTIONS.headers) !== null && _a !== void 0 ? _a : {}), (_b = globalOptions === null || globalOptions === void 0 ? void 0 : globalOptions.headers) !== null && _b !== void 0 ? _b : {})\n        }),\n        accessToken: ()=>__awaiter(this, void 0, void 0, function*() {\n                return '';\n            })\n    };\n    if (options.accessToken) {\n        result.accessToken = options.accessToken;\n    } else {\n        // hack around Required<>\n        delete result.accessToken;\n    }\n    return result;\n} //# sourceMappingURL=helpers.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/lib/helpers.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/lib/version.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/lib/version.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\nconst version = '2.52.1'; //# sourceMappingURL=version.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTIuMS9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3N1cGFiYXNlLWpzL2Rpc3QvbW9kdWxlL2xpYi92ZXJzaW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTyxNQUFNLE9BQU8sR0FBRyxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccGNcXERlc2t0b3BcXHNyY1xcbGliXFx2ZXJzaW9uLnRzIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/lib/version.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/SupabaseClient.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/SupabaseClient.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SupabaseClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_functions_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @supabase/functions-js */ \"(ssr)/./node_modules/.pnpm/@supabase+functions-js@2.4.5/node_modules/@supabase/functions-js/dist/module/FunctionsClient.js\");\n/* harmony import */ var _supabase_postgrest_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/postgrest-js */ \"(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs\");\n/* harmony import */ var _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/realtime-js */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/index.js\");\n/* harmony import */ var _supabase_storage_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @supabase/storage-js */ \"(ssr)/./node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/StorageClient.js\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/constants */ \"(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/lib/constants.js\");\n/* harmony import */ var _lib_fetch__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lib/fetch */ \"(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/lib/fetch.js\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/helpers */ \"(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/lib/helpers.js\");\n/* harmony import */ var _lib_SupabaseAuthClient__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./lib/SupabaseAuthClient */ \"(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/lib/SupabaseAuthClient.js\");\nvar __awaiter = undefined && undefined.__awaiter || function(thisArg, _arguments, P, generator) {\n    function adopt(value) {\n        return value instanceof P ? value : new P(function(resolve) {\n            resolve(value);\n        });\n    }\n    return new (P || (P = Promise))(function(resolve, reject) {\n        function fulfilled(value) {\n            try {\n                step(generator.next(value));\n            } catch (e) {\n                reject(e);\n            }\n        }\n        function rejected(value) {\n            try {\n                step(generator[\"throw\"](value));\n            } catch (e) {\n                reject(e);\n            }\n        }\n        function step(result) {\n            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n        }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n\n\n\n\n\n\n\n\n/**\n * Supabase Client.\n *\n * An isomorphic Javascript client for interacting with Postgres.\n */ class SupabaseClient {\n    /**\n     * Create a new client for use in the browser.\n     * @param supabaseUrl The unique Supabase URL which is supplied when you create a new project in your project dashboard.\n     * @param supabaseKey The unique Supabase Key which is supplied when you create a new project in your project dashboard.\n     * @param options.db.schema You can switch in between schemas. The schema needs to be on the list of exposed schemas inside Supabase.\n     * @param options.auth.autoRefreshToken Set to \"true\" if you want to automatically refresh the token before expiring.\n     * @param options.auth.persistSession Set to \"true\" if you want to automatically save the user session into local storage.\n     * @param options.auth.detectSessionInUrl Set to \"true\" if you want to automatically detects OAuth grants in the URL and signs in the user.\n     * @param options.realtime Options passed along to realtime-js constructor.\n     * @param options.global.fetch A custom fetch implementation.\n     * @param options.global.headers Any additional headers to send with each network request.\n     */ constructor(supabaseUrl, supabaseKey, options){\n        var _a, _b, _c;\n        this.supabaseUrl = supabaseUrl;\n        this.supabaseKey = supabaseKey;\n        if (!supabaseUrl) throw new Error('supabaseUrl is required.');\n        if (!supabaseKey) throw new Error('supabaseKey is required.');\n        const _supabaseUrl = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_2__.ensureTrailingSlash)(supabaseUrl);\n        const baseUrl = new URL(_supabaseUrl);\n        this.realtimeUrl = new URL('realtime/v1', baseUrl);\n        this.realtimeUrl.protocol = this.realtimeUrl.protocol.replace('http', 'ws');\n        this.authUrl = new URL('auth/v1', baseUrl);\n        this.storageUrl = new URL('storage/v1', baseUrl);\n        this.functionsUrl = new URL('functions/v1', baseUrl);\n        // default storage key uses the supabase project ref as a namespace\n        const defaultStorageKey = `sb-${baseUrl.hostname.split('.')[0]}-auth-token`;\n        const DEFAULTS = {\n            db: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_DB_OPTIONS,\n            realtime: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_REALTIME_OPTIONS,\n            auth: Object.assign(Object.assign({}, _lib_constants__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_AUTH_OPTIONS), {\n                storageKey: defaultStorageKey\n            }),\n            global: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_GLOBAL_OPTIONS\n        };\n        const settings = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_2__.applySettingDefaults)(options !== null && options !== void 0 ? options : {}, DEFAULTS);\n        this.storageKey = (_a = settings.auth.storageKey) !== null && _a !== void 0 ? _a : '';\n        this.headers = (_b = settings.global.headers) !== null && _b !== void 0 ? _b : {};\n        if (!settings.accessToken) {\n            this.auth = this._initSupabaseAuthClient((_c = settings.auth) !== null && _c !== void 0 ? _c : {}, this.headers, settings.global.fetch);\n        } else {\n            this.accessToken = settings.accessToken;\n            this.auth = new Proxy({}, {\n                get: (_, prop)=>{\n                    throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(prop)} is not possible`);\n                }\n            });\n        }\n        this.fetch = (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_4__.fetchWithAuth)(supabaseKey, this._getAccessToken.bind(this), settings.global.fetch);\n        this.realtime = this._initRealtimeClient(Object.assign({\n            headers: this.headers,\n            accessToken: this._getAccessToken.bind(this)\n        }, settings.realtime));\n        this.rest = new _supabase_postgrest_js__WEBPACK_IMPORTED_MODULE_0__.PostgrestClient(new URL('rest/v1', baseUrl).href, {\n            headers: this.headers,\n            schema: settings.db.schema,\n            fetch: this.fetch\n        });\n        if (!settings.accessToken) {\n            this._listenForAuthEvents();\n        }\n    }\n    /**\n     * Supabase Functions allows you to deploy and invoke edge functions.\n     */ get functions() {\n        return new _supabase_functions_js__WEBPACK_IMPORTED_MODULE_5__.FunctionsClient(this.functionsUrl.href, {\n            headers: this.headers,\n            customFetch: this.fetch\n        });\n    }\n    /**\n     * Supabase Storage allows you to manage user-generated content, such as photos or videos.\n     */ get storage() {\n        return new _supabase_storage_js__WEBPACK_IMPORTED_MODULE_6__.StorageClient(this.storageUrl.href, this.headers, this.fetch);\n    }\n    /**\n     * Perform a query on a table or a view.\n     *\n     * @param relation - The table or view name to query\n     */ from(relation) {\n        return this.rest.from(relation);\n    }\n    // NOTE: signatures must be kept in sync with PostgrestClient.schema\n    /**\n     * Select a schema to query or perform an function (rpc) call.\n     *\n     * The schema needs to be on the list of exposed schemas inside Supabase.\n     *\n     * @param schema - The schema to query\n     */ schema(schema) {\n        return this.rest.schema(schema);\n    }\n    // NOTE: signatures must be kept in sync with PostgrestClient.rpc\n    /**\n     * Perform a function call.\n     *\n     * @param fn - The function name to call\n     * @param args - The arguments to pass to the function call\n     * @param options - Named parameters\n     * @param options.head - When set to `true`, `data` will not be returned.\n     * Useful if you only need the count.\n     * @param options.get - When set to `true`, the function will be called with\n     * read-only access mode.\n     * @param options.count - Count algorithm to use to count rows returned by the\n     * function. Only applicable for [set-returning\n     * functions](https://www.postgresql.org/docs/current/functions-srf.html).\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     */ rpc(fn, args = {}, options = {}) {\n        return this.rest.rpc(fn, args, options);\n    }\n    /**\n     * Creates a Realtime channel with Broadcast, Presence, and Postgres Changes.\n     *\n     * @param {string} name - The name of the Realtime channel.\n     * @param {Object} opts - The options to pass to the Realtime channel.\n     *\n     */ channel(name, opts = {\n        config: {}\n    }) {\n        return this.realtime.channel(name, opts);\n    }\n    /**\n     * Returns all Realtime channels.\n     */ getChannels() {\n        return this.realtime.getChannels();\n    }\n    /**\n     * Unsubscribes and removes Realtime channel from Realtime client.\n     *\n     * @param {RealtimeChannel} channel - The name of the Realtime channel.\n     *\n     */ removeChannel(channel) {\n        return this.realtime.removeChannel(channel);\n    }\n    /**\n     * Unsubscribes and removes all Realtime channels from Realtime client.\n     */ removeAllChannels() {\n        return this.realtime.removeAllChannels();\n    }\n    _getAccessToken() {\n        var _a, _b;\n        return __awaiter(this, void 0, void 0, function*() {\n            if (this.accessToken) {\n                return yield this.accessToken();\n            }\n            const { data } = yield this.auth.getSession();\n            return (_b = (_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token) !== null && _b !== void 0 ? _b : null;\n        });\n    }\n    _initSupabaseAuthClient({ autoRefreshToken, persistSession, detectSessionInUrl, storage, storageKey, flowType, lock, debug }, headers, fetch) {\n        const authHeaders = {\n            Authorization: `Bearer ${this.supabaseKey}`,\n            apikey: `${this.supabaseKey}`\n        };\n        return new _lib_SupabaseAuthClient__WEBPACK_IMPORTED_MODULE_7__.SupabaseAuthClient({\n            url: this.authUrl.href,\n            headers: Object.assign(Object.assign({}, authHeaders), headers),\n            storageKey: storageKey,\n            autoRefreshToken,\n            persistSession,\n            detectSessionInUrl,\n            storage,\n            flowType,\n            lock,\n            debug,\n            fetch,\n            // auth checks if there is a custom authorizaiton header using this flag\n            // so it knows whether to return an error when getUser is called with no session\n            hasCustomAuthorizationHeader: 'Authorization' in this.headers\n        });\n    }\n    _initRealtimeClient(options) {\n        return new _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_1__.RealtimeClient(this.realtimeUrl.href, Object.assign(Object.assign({}, options), {\n            params: Object.assign({\n                apikey: this.supabaseKey\n            }, options === null || options === void 0 ? void 0 : options.params)\n        }));\n    }\n    _listenForAuthEvents() {\n        let data = this.auth.onAuthStateChange((event, session)=>{\n            this._handleTokenChanged(event, 'CLIENT', session === null || session === void 0 ? void 0 : session.access_token);\n        });\n        return data;\n    }\n    _handleTokenChanged(event, source, token) {\n        if ((event === 'TOKEN_REFRESHED' || event === 'SIGNED_IN') && this.changedAccessToken !== token) {\n            this.changedAccessToken = token;\n        } else if (event === 'SIGNED_OUT') {\n            this.realtime.setAuth();\n            if (source == 'STORAGE') this.auth.signOut();\n            this.changedAccessToken = undefined;\n        }\n    }\n} //# sourceMappingURL=SupabaseClient.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/SupabaseClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/index.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/index.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthAdminApi: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthAdminApi),\n/* harmony export */   AuthApiError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthApiError),\n/* harmony export */   AuthClient: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthClient),\n/* harmony export */   AuthError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthError),\n/* harmony export */   AuthImplicitGrantRedirectError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthImplicitGrantRedirectError),\n/* harmony export */   AuthInvalidCredentialsError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthInvalidCredentialsError),\n/* harmony export */   AuthInvalidJwtError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthInvalidJwtError),\n/* harmony export */   AuthInvalidTokenResponseError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthInvalidTokenResponseError),\n/* harmony export */   AuthPKCEGrantCodeExchangeError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthPKCEGrantCodeExchangeError),\n/* harmony export */   AuthRetryableFetchError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthRetryableFetchError),\n/* harmony export */   AuthSessionMissingError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthSessionMissingError),\n/* harmony export */   AuthUnknownError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthUnknownError),\n/* harmony export */   AuthWeakPasswordError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthWeakPasswordError),\n/* harmony export */   CustomAuthError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.CustomAuthError),\n/* harmony export */   FunctionRegion: () => (/* reexport safe */ _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__.FunctionRegion),\n/* harmony export */   FunctionsError: () => (/* reexport safe */ _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__.FunctionsError),\n/* harmony export */   FunctionsFetchError: () => (/* reexport safe */ _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__.FunctionsFetchError),\n/* harmony export */   FunctionsHttpError: () => (/* reexport safe */ _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__.FunctionsHttpError),\n/* harmony export */   FunctionsRelayError: () => (/* reexport safe */ _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__.FunctionsRelayError),\n/* harmony export */   GoTrueAdminApi: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.GoTrueAdminApi),\n/* harmony export */   GoTrueClient: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.GoTrueClient),\n/* harmony export */   NavigatorLockAcquireTimeoutError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.NavigatorLockAcquireTimeoutError),\n/* harmony export */   PostgrestError: () => (/* reexport safe */ _supabase_postgrest_js__WEBPACK_IMPORTED_MODULE_1__.PostgrestError),\n/* harmony export */   REALTIME_CHANNEL_STATES: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.REALTIME_CHANNEL_STATES),\n/* harmony export */   REALTIME_LISTEN_TYPES: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.REALTIME_LISTEN_TYPES),\n/* harmony export */   REALTIME_POSTGRES_CHANGES_LISTEN_EVENT: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.REALTIME_POSTGRES_CHANGES_LISTEN_EVENT),\n/* harmony export */   REALTIME_PRESENCE_LISTEN_EVENTS: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.REALTIME_PRESENCE_LISTEN_EVENTS),\n/* harmony export */   REALTIME_SUBSCRIBE_STATES: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.REALTIME_SUBSCRIBE_STATES),\n/* harmony export */   RealtimeChannel: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.RealtimeChannel),\n/* harmony export */   RealtimeClient: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.RealtimeClient),\n/* harmony export */   RealtimePresence: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.RealtimePresence),\n/* harmony export */   SIGN_OUT_SCOPES: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.SIGN_OUT_SCOPES),\n/* harmony export */   SupabaseClient: () => (/* reexport safe */ _SupabaseClient__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   createClient: () => (/* binding */ createClient),\n/* harmony export */   isAuthApiError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthApiError),\n/* harmony export */   isAuthError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthError),\n/* harmony export */   isAuthImplicitGrantRedirectError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthImplicitGrantRedirectError),\n/* harmony export */   isAuthRetryableFetchError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthRetryableFetchError),\n/* harmony export */   isAuthSessionMissingError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthSessionMissingError),\n/* harmony export */   isAuthWeakPasswordError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthWeakPasswordError),\n/* harmony export */   lockInternals: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.lockInternals),\n/* harmony export */   navigatorLock: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.navigatorLock),\n/* harmony export */   processLock: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.processLock)\n/* harmony export */ });\n/* harmony import */ var _SupabaseClient__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./SupabaseClient */ \"(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/SupabaseClient.js\");\n/* harmony import */ var _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-js */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/index.js\");\n/* harmony import */ var _supabase_postgrest_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/postgrest-js */ \"(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs\");\n/* harmony import */ var _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/functions-js */ \"(ssr)/./node_modules/.pnpm/@supabase+functions-js@2.4.5/node_modules/@supabase/functions-js/dist/module/types.js\");\n/* harmony import */ var _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @supabase/realtime-js */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/index.js\");\n\n\n\n\n\n\n/**\n * Creates a new Supabase Client.\n */ const createClient = (supabaseUrl, supabaseKey, options)=>{\n    return new _SupabaseClient__WEBPACK_IMPORTED_MODULE_4__[\"default\"](supabaseUrl, supabaseKey, options);\n};\n// Check for Node.js <= 18 deprecation\nfunction shouldShowDeprecationWarning() {\n    if ( false || typeof process === 'undefined' || process.version === undefined || process.version === null) {\n        return false;\n    }\n    const versionMatch = process.version.match(/^v(\\d+)\\./);\n    if (!versionMatch) {\n        return false;\n    }\n    const majorVersion = parseInt(versionMatch[1], 10);\n    return majorVersion <= 18;\n}\nif (shouldShowDeprecationWarning()) {\n    console.warn(`⚠️  Node.js 18 and below are deprecated and will no longer be supported in future versions of @supabase/supabase-js. ` + `Please upgrade to Node.js 20 or later. ` + `For more information, visit: https://github.com/orgs/supabase/discussions/37217`);\n} //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTIuMS9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3N1cGFiYXNlLWpzL2Rpc3QvbW9kdWxlL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUE2QztBQUdaO0FBT0Y7QUFRQTtBQUNNO0FBQ3VCO0FBRzVEOztHQUVHLENBQ0ksTUFBTSxZQUFZLEdBQUcsQ0FTMUIsV0FBbUIsRUFDbkIsV0FBbUIsRUFDbkIsT0FBMkMsRUFDRyxFQUFFO0lBQ2hELE9BQU8sSUFBSSx1REFBYyxDQUErQixXQUFXLEVBQUUsV0FBVyxFQUFFLE9BQU8sQ0FBQztBQUM1RixDQUFDO0FBRUQsc0NBQXNDO0FBQ3RDLFNBQVMsNEJBQTRCO0lBQ25DLElBQ0UsTUFBNkIsSUFDN0IsT0FBTyxPQUFPLEtBQUssV0FBVyxJQUM5QixPQUFPLENBQUMsT0FBTyxLQUFLLFNBQVMsSUFDN0IsT0FBTyxDQUFDLE9BQU8sS0FBSyxJQUFJLEVBQ3hCO1FBQ0EsT0FBTyxLQUFLO0tBQ2I7SUFFRCxNQUFNLFlBQVksR0FBRyxPQUFPLENBQUMsT0FBTyxDQUFDLEtBQUssQ0FBQyxXQUFXLENBQUM7SUFDdkQsSUFBSSxDQUFDLFlBQVksRUFBRTtRQUNqQixPQUFPLEtBQUs7S0FDYjtJQUVELE1BQU0sWUFBWSxHQUFHLFFBQVEsQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLEVBQUUsRUFBRSxDQUFDO0lBQ2xELE9BQU8sWUFBWSxJQUFJLEVBQUU7QUFDM0IsQ0FBQztBQUVELElBQUksNEJBQTRCLEVBQUUsRUFBRTtJQUNsQyxPQUFPLENBQUMsSUFBSSxDQUNWLHVIQUF1SCxHQUNySCx5Q0FBeUMsR0FDekMsaUZBQWlGLENBQ3BGO0NBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccGNcXERlc2t0b3BcXFByb2plY3RzXFxzcmNcXGluZGV4LnRzIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/lib/SupabaseAuthClient.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/lib/SupabaseAuthClient.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupabaseAuthClient: () => (/* binding */ SupabaseAuthClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-js */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/index.js\");\n\nclass SupabaseAuthClient extends _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthClient {\n    constructor(options){\n        super(options);\n    }\n} //# sourceMappingURL=SupabaseAuthClient.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTIuMS9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3N1cGFiYXNlLWpzL2Rpc3QvbW9kdWxlL2xpYi9TdXBhYmFzZUF1dGhDbGllbnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBOEM7QUFHeEMsTUFBTyxrQkFBbUIsU0FBUSx5REFBVTtJQUNoRCxZQUFZLE9BQWtDO1FBQzVDLEtBQUssQ0FBQyxPQUFPLENBQUM7SUFDaEIsQ0FBQztDQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHBjXFxEZXNrdG9wXFxzcmNcXGxpYlxcU3VwYWJhc2VBdXRoQ2xpZW50LnRzIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/lib/SupabaseAuthClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/lib/constants.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/lib/constants.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_AUTH_OPTIONS: () => (/* binding */ DEFAULT_AUTH_OPTIONS),\n/* harmony export */   DEFAULT_DB_OPTIONS: () => (/* binding */ DEFAULT_DB_OPTIONS),\n/* harmony export */   DEFAULT_GLOBAL_OPTIONS: () => (/* binding */ DEFAULT_GLOBAL_OPTIONS),\n/* harmony export */   DEFAULT_HEADERS: () => (/* binding */ DEFAULT_HEADERS),\n/* harmony export */   DEFAULT_REALTIME_OPTIONS: () => (/* binding */ DEFAULT_REALTIME_OPTIONS)\n/* harmony export */ });\n/* harmony import */ var _version__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./version */ \"(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/lib/version.js\");\n\nlet JS_ENV = '';\n// @ts-ignore\nif (typeof Deno !== 'undefined') {\n    JS_ENV = 'deno';\n} else if (typeof document !== 'undefined') {\n    JS_ENV = 'web';\n} else if (typeof navigator !== 'undefined' && navigator.product === 'ReactNative') {\n    JS_ENV = 'react-native';\n} else {\n    JS_ENV = 'node';\n}\nconst DEFAULT_HEADERS = {\n    'X-Client-Info': `supabase-js-${JS_ENV}/${_version__WEBPACK_IMPORTED_MODULE_0__.version}`\n};\nconst DEFAULT_GLOBAL_OPTIONS = {\n    headers: DEFAULT_HEADERS\n};\nconst DEFAULT_DB_OPTIONS = {\n    schema: 'public'\n};\nconst DEFAULT_AUTH_OPTIONS = {\n    autoRefreshToken: true,\n    persistSession: true,\n    detectSessionInUrl: true,\n    flowType: 'implicit'\n};\nconst DEFAULT_REALTIME_OPTIONS = {}; //# sourceMappingURL=constants.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTIuMS9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3N1cGFiYXNlLWpzL2Rpc3QvbW9kdWxlL2xpYi9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBR21DO0FBRW5DLElBQUksTUFBTSxHQUFHLEVBQUU7QUFDZixhQUFhO0FBQ2IsSUFBSSxPQUFPLElBQUksS0FBSyxXQUFXLEVBQUU7SUFDL0IsTUFBTSxHQUFHLE1BQU07Q0FDaEIsTUFBTSxJQUFJLE9BQU8sUUFBUSxLQUFLLFdBQVcsRUFBRTtJQUMxQyxNQUFNLEdBQUcsS0FBSztDQUNmLE1BQU0sSUFBSSxPQUFPLFNBQVMsS0FBSyxXQUFXLElBQUksU0FBUyxDQUFDLE9BQU8sS0FBSyxhQUFhLEVBQUU7SUFDbEYsTUFBTSxHQUFHLGNBQWM7Q0FDeEIsTUFBTTtJQUNMLE1BQU0sR0FBRyxNQUFNO0NBQ2hCO0FBRU0sTUFBTSxlQUFlLEdBQUc7SUFBRSxlQUFlLEVBQUUsZUFBZSxNQUFNLElBQUksNkNBQU8sRUFBRTtBQUFBLENBQUU7QUFFL0UsTUFBTSxzQkFBc0IsR0FBRztJQUNwQyxPQUFPLEVBQUUsZUFBZTtDQUN6QjtBQUVNLE1BQU0sa0JBQWtCLEdBQUc7SUFDaEMsTUFBTSxFQUFFLFFBQVE7Q0FDakI7QUFFTSxNQUFNLG9CQUFvQixHQUE4QjtJQUM3RCxnQkFBZ0IsRUFBRSxJQUFJO0lBQ3RCLGNBQWMsRUFBRSxJQUFJO0lBQ3BCLGtCQUFrQixFQUFFLElBQUk7SUFDeEIsUUFBUSxFQUFFLFVBQVU7Q0FDckI7QUFFTSxNQUFNLHdCQUF3QixHQUEwQixFQUFFIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHBjXFxEZXNrdG9wXFxzcmNcXGxpYlxcY29uc3RhbnRzLnRzIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/lib/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/lib/fetch.js":
/*!*********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/lib/fetch.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fetchWithAuth: () => (/* binding */ fetchWithAuth),\n/* harmony export */   resolveFetch: () => (/* binding */ resolveFetch),\n/* harmony export */   resolveHeadersConstructor: () => (/* binding */ resolveHeadersConstructor)\n/* harmony export */ });\n/* harmony import */ var _supabase_node_fetch__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/node-fetch */ \"(ssr)/./node_modules/.pnpm/@supabase+node-fetch@2.6.15/node_modules/@supabase/node-fetch/lib/index.js\");\n/* harmony import */ var _supabase_node_fetch__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_node_fetch__WEBPACK_IMPORTED_MODULE_0__);\nvar __awaiter = undefined && undefined.__awaiter || function(thisArg, _arguments, P, generator) {\n    function adopt(value) {\n        return value instanceof P ? value : new P(function(resolve) {\n            resolve(value);\n        });\n    }\n    return new (P || (P = Promise))(function(resolve, reject) {\n        function fulfilled(value) {\n            try {\n                step(generator.next(value));\n            } catch (e) {\n                reject(e);\n            }\n        }\n        function rejected(value) {\n            try {\n                step(generator[\"throw\"](value));\n            } catch (e) {\n                reject(e);\n            }\n        }\n        function step(result) {\n            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n        }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n// @ts-ignore\n\nconst resolveFetch = (customFetch)=>{\n    let _fetch;\n    if (customFetch) {\n        _fetch = customFetch;\n    } else if (typeof fetch === 'undefined') {\n        _fetch = (_supabase_node_fetch__WEBPACK_IMPORTED_MODULE_0___default());\n    } else {\n        _fetch = fetch;\n    }\n    return (...args)=>_fetch(...args);\n};\nconst resolveHeadersConstructor = ()=>{\n    if (typeof Headers === 'undefined') {\n        return _supabase_node_fetch__WEBPACK_IMPORTED_MODULE_0__.Headers;\n    }\n    return Headers;\n};\nconst fetchWithAuth = (supabaseKey, getAccessToken, customFetch)=>{\n    const fetch1 = resolveFetch(customFetch);\n    const HeadersConstructor = resolveHeadersConstructor();\n    return (input, init)=>__awaiter(void 0, void 0, void 0, function*() {\n            var _a;\n            const accessToken = (_a = yield getAccessToken()) !== null && _a !== void 0 ? _a : supabaseKey;\n            let headers = new HeadersConstructor(init === null || init === void 0 ? void 0 : init.headers);\n            if (!headers.has('apikey')) {\n                headers.set('apikey', supabaseKey);\n            }\n            if (!headers.has('Authorization')) {\n                headers.set('Authorization', `Bearer ${accessToken}`);\n            }\n            return fetch1(input, Object.assign(Object.assign({}, init), {\n                headers\n            }));\n        });\n}; //# sourceMappingURL=fetch.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTIuMS9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3N1cGFiYXNlLWpzL2Rpc3QvbW9kdWxlL2xpYi9mZXRjaC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLGFBQWE7QUFDZ0U7QUFJdEUsTUFBTSxZQUFZLEdBQUcsQ0FBQyxXQUFtQixFQUFTLEVBQUU7SUFDekQsSUFBSSxNQUFhO0lBQ2pCLElBQUksV0FBVyxFQUFFO1FBQ2YsTUFBTSxHQUFHLFdBQVc7S0FDckIsTUFBTSxJQUFJLE9BQU8sS0FBSyxLQUFLLFdBQVcsRUFBRTtRQUN2QyxNQUFNLEdBQUcsNkRBQTZCO0tBQ3ZDLE1BQU07UUFDTCxNQUFNLEdBQUcsS0FBSztLQUNmO0lBQ0QsT0FBTyxDQUFDLEdBQUcsSUFBdUIsRUFBRSxDQUFHLENBQUQsS0FBTyxDQUFDLEdBQUcsSUFBSSxDQUFDO0FBQ3hELENBQUM7QUFFTSxNQUFNLHlCQUF5QixHQUFHLEdBQUcsRUFBRTtJQUM1QyxJQUFJLE9BQU8sT0FBTyxLQUFLLFdBQVcsRUFBRTtRQUNsQyxPQUFPLHlEQUFnQjtLQUN4QjtJQUVELE9BQU8sT0FBTztBQUNoQixDQUFDO0FBRU0sTUFBTSxhQUFhLEdBQUcsQ0FDM0IsV0FBbUIsRUFDbkIsY0FBNEMsRUFDNUMsV0FBbUIsRUFDWixFQUFFO0lBQ1QsTUFBTSxLQUFLLElBQUcsWUFBWSxDQUFDLFdBQVcsQ0FBQztJQUN2QyxNQUFNLGtCQUFrQixHQUFHLHlCQUF5QixFQUFFO0lBRXRELE9BQU8sQ0FBTyxLQUFLLEVBQUUsSUFBSSxFQUFFLENBQUU7O1lBQzNCLE1BQU0sV0FBVyxHQUFHLFlBQU8sY0FBYyxFQUFFLENBQUMsa0NBQUksV0FBVztZQUMzRCxJQUFJLE9BQU8sR0FBRyxJQUFJLGtCQUFrQixDQUFDLElBQUksYUFBSixJQUFJLHVCQUFKLElBQUksQ0FBRSxPQUFPLENBQUM7WUFFbkQsSUFBSSxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsUUFBUSxDQUFDLEVBQUU7Z0JBQzFCLE9BQU8sQ0FBQyxHQUFHLENBQUMsUUFBUSxFQUFFLFdBQVcsQ0FBQzthQUNuQztZQUVELElBQUksQ0FBQyxPQUFPLENBQUMsR0FBRyxDQUFDLGVBQWUsQ0FBQyxFQUFFO2dCQUNqQyxPQUFPLENBQUMsR0FBRyxDQUFDLGVBQWUsRUFBRSxVQUFVLFdBQVcsRUFBRSxDQUFDO2FBQ3REO1lBRUQsT0FBTyxLQUFLLEVBQUMsS0FBSyxrQ0FBTyxJQUFJO2dCQUFFLE9BQU87WUFBQSxHQUFHO1FBQzNDLENBQUM7QUFDSCxDQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHBjXFxEZXNrdG9wXFxzcmNcXGxpYlxcZmV0Y2gudHMiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/lib/fetch.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/lib/helpers.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/lib/helpers.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applySettingDefaults: () => (/* binding */ applySettingDefaults),\n/* harmony export */   ensureTrailingSlash: () => (/* binding */ ensureTrailingSlash),\n/* harmony export */   isBrowser: () => (/* binding */ isBrowser),\n/* harmony export */   uuid: () => (/* binding */ uuid)\n/* harmony export */ });\nvar __awaiter = undefined && undefined.__awaiter || function(thisArg, _arguments, P, generator) {\n    function adopt(value) {\n        return value instanceof P ? value : new P(function(resolve) {\n            resolve(value);\n        });\n    }\n    return new (P || (P = Promise))(function(resolve, reject) {\n        function fulfilled(value) {\n            try {\n                step(generator.next(value));\n            } catch (e) {\n                reject(e);\n            }\n        }\n        function rejected(value) {\n            try {\n                step(generator[\"throw\"](value));\n            } catch (e) {\n                reject(e);\n            }\n        }\n        function step(result) {\n            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n        }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nfunction uuid() {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\n        var r = Math.random() * 16 | 0, v = c == 'x' ? r : r & 0x3 | 0x8;\n        return v.toString(16);\n    });\n}\nfunction ensureTrailingSlash(url) {\n    return url.endsWith('/') ? url : url + '/';\n}\nconst isBrowser = ()=>\"undefined\" !== 'undefined';\nfunction applySettingDefaults(options, defaults) {\n    var _a, _b;\n    const { db: dbOptions, auth: authOptions, realtime: realtimeOptions, global: globalOptions } = options;\n    const { db: DEFAULT_DB_OPTIONS, auth: DEFAULT_AUTH_OPTIONS, realtime: DEFAULT_REALTIME_OPTIONS, global: DEFAULT_GLOBAL_OPTIONS } = defaults;\n    const result = {\n        db: Object.assign(Object.assign({}, DEFAULT_DB_OPTIONS), dbOptions),\n        auth: Object.assign(Object.assign({}, DEFAULT_AUTH_OPTIONS), authOptions),\n        realtime: Object.assign(Object.assign({}, DEFAULT_REALTIME_OPTIONS), realtimeOptions),\n        global: Object.assign(Object.assign(Object.assign({}, DEFAULT_GLOBAL_OPTIONS), globalOptions), {\n            headers: Object.assign(Object.assign({}, (_a = DEFAULT_GLOBAL_OPTIONS === null || DEFAULT_GLOBAL_OPTIONS === void 0 ? void 0 : DEFAULT_GLOBAL_OPTIONS.headers) !== null && _a !== void 0 ? _a : {}), (_b = globalOptions === null || globalOptions === void 0 ? void 0 : globalOptions.headers) !== null && _b !== void 0 ? _b : {})\n        }),\n        accessToken: ()=>__awaiter(this, void 0, void 0, function*() {\n                return '';\n            })\n    };\n    if (options.accessToken) {\n        result.accessToken = options.accessToken;\n    } else {\n        // hack around Required<>\n        delete result.accessToken;\n    }\n    return result;\n} //# sourceMappingURL=helpers.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/lib/helpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/lib/version.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/lib/version.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\nconst version = '2.52.1'; //# sourceMappingURL=version.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTIuMS9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3N1cGFiYXNlLWpzL2Rpc3QvbW9kdWxlL2xpYi92ZXJzaW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTyxNQUFNLE9BQU8sR0FBRyxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccGNcXERlc2t0b3BcXHNyY1xcbGliXFx2ZXJzaW9uLnRzIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.52.1/node_modules/@supabase/supabase-js/dist/module/lib/version.js\n");

/***/ })

};
;