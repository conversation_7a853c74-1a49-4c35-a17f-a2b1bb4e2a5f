"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tanstack+react-query-devto_f88dc5b4961f1480dcf94855f5092913";
exports.ids = ["vendor-chunks/@tanstack+react-query-devto_f88dc5b4961f1480dcf94855f5092913"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tanstack+react-query-devto_f88dc5b4961f1480dcf94855f5092913/node_modules/@tanstack/react-query-devtools/build/modern/ReactQueryDevtools.js":
/*!************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tanstack+react-query-devto_f88dc5b4961f1480dcf94855f5092913/node_modules/@tanstack/react-query-devtools/build/modern/ReactQueryDevtools.js ***!
  \************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReactQueryDevtools: () => (/* binding */ ReactQueryDevtools)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.4.4_@babel+core@7.2_0b8959437c64d880cd310af1c435e779/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/.pnpm/@tanstack+react-query@5.84.0_react@19.1.0/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/.pnpm/@tanstack+query-core@5.83.1/node_modules/@tanstack/query-core/build/modern/onlineManager.js\");\n/* harmony import */ var _tanstack_query_devtools__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/query-devtools */ \"(ssr)/./node_modules/.pnpm/@tanstack+query-devtools@5.84.0/node_modules/@tanstack/query-devtools/build/dev.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.4_@babel+core@7.2_0b8959437c64d880cd310af1c435e779/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ ReactQueryDevtools auto */ // src/ReactQueryDevtools.tsx\n\n\n\n\nfunction ReactQueryDevtools(props) {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)(props.client);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const { buttonPosition, position, initialIsOpen, errorTypes, styleNonce, shadowDOMTarget, hideDisabledQueries } = props;\n    const [devtools] = react__WEBPACK_IMPORTED_MODULE_0__.useState(new _tanstack_query_devtools__WEBPACK_IMPORTED_MODULE_1__.TanstackQueryDevtools({\n        client: queryClient,\n        queryFlavor: \"React Query\",\n        version: \"5\",\n        onlineManager: _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.onlineManager,\n        buttonPosition,\n        position,\n        initialIsOpen,\n        errorTypes,\n        styleNonce,\n        shadowDOMTarget,\n        hideDisabledQueries\n    }));\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ReactQueryDevtools.useEffect\": ()=>{\n            devtools.setClient(queryClient);\n        }\n    }[\"ReactQueryDevtools.useEffect\"], [\n        queryClient,\n        devtools\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ReactQueryDevtools.useEffect\": ()=>{\n            if (buttonPosition) {\n                devtools.setButtonPosition(buttonPosition);\n            }\n        }\n    }[\"ReactQueryDevtools.useEffect\"], [\n        buttonPosition,\n        devtools\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ReactQueryDevtools.useEffect\": ()=>{\n            if (position) {\n                devtools.setPosition(position);\n            }\n        }\n    }[\"ReactQueryDevtools.useEffect\"], [\n        position,\n        devtools\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ReactQueryDevtools.useEffect\": ()=>{\n            devtools.setInitialIsOpen(initialIsOpen || false);\n        }\n    }[\"ReactQueryDevtools.useEffect\"], [\n        initialIsOpen,\n        devtools\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ReactQueryDevtools.useEffect\": ()=>{\n            devtools.setErrorTypes(errorTypes || []);\n        }\n    }[\"ReactQueryDevtools.useEffect\"], [\n        errorTypes,\n        devtools\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ReactQueryDevtools.useEffect\": ()=>{\n            if (ref.current) {\n                devtools.mount(ref.current);\n            }\n            return ({\n                \"ReactQueryDevtools.useEffect\": ()=>{\n                    devtools.unmount();\n                }\n            })[\"ReactQueryDevtools.useEffect\"];\n        }\n    }[\"ReactQueryDevtools.useEffect\"], [\n        devtools\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n        dir: \"ltr\",\n        className: \"tsqd-parent-container\",\n        ref\n    });\n}\n //# sourceMappingURL=ReactQueryDevtools.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tanstack+react-query-devto_f88dc5b4961f1480dcf94855f5092913/node_modules/@tanstack/react-query-devtools/build/modern/ReactQueryDevtools.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@tanstack+react-query-devto_f88dc5b4961f1480dcf94855f5092913/node_modules/@tanstack/react-query-devtools/build/modern/ReactQueryDevtoolsPanel.js":
/*!*****************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tanstack+react-query-devto_f88dc5b4961f1480dcf94855f5092913/node_modules/@tanstack/react-query-devtools/build/modern/ReactQueryDevtoolsPanel.js ***!
  \*****************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReactQueryDevtoolsPanel: () => (/* binding */ ReactQueryDevtoolsPanel)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.4.4_@babel+core@7.2_0b8959437c64d880cd310af1c435e779/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/.pnpm/@tanstack+react-query@5.84.0_react@19.1.0/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/.pnpm/@tanstack+query-core@5.83.1/node_modules/@tanstack/query-core/build/modern/onlineManager.js\");\n/* harmony import */ var _tanstack_query_devtools__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/query-devtools */ \"(ssr)/./node_modules/.pnpm/@tanstack+query-devtools@5.84.0/node_modules/@tanstack/query-devtools/build/dev.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.4_@babel+core@7.2_0b8959437c64d880cd310af1c435e779/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ ReactQueryDevtoolsPanel auto */ // src/ReactQueryDevtoolsPanel.tsx\n\n\n\n\nfunction ReactQueryDevtoolsPanel(props) {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)(props.client);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const { errorTypes, styleNonce, shadowDOMTarget, hideDisabledQueries } = props;\n    const [devtools] = react__WEBPACK_IMPORTED_MODULE_0__.useState(new _tanstack_query_devtools__WEBPACK_IMPORTED_MODULE_1__.TanstackQueryDevtoolsPanel({\n        client: queryClient,\n        queryFlavor: \"React Query\",\n        version: \"5\",\n        onlineManager: _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.onlineManager,\n        buttonPosition: \"bottom-left\",\n        position: \"bottom\",\n        initialIsOpen: true,\n        errorTypes,\n        styleNonce,\n        shadowDOMTarget,\n        onClose: props.onClose,\n        hideDisabledQueries\n    }));\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ReactQueryDevtoolsPanel.useEffect\": ()=>{\n            devtools.setClient(queryClient);\n        }\n    }[\"ReactQueryDevtoolsPanel.useEffect\"], [\n        queryClient,\n        devtools\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ReactQueryDevtoolsPanel.useEffect\": ()=>{\n            devtools.setOnClose(props.onClose ?? ({\n                \"ReactQueryDevtoolsPanel.useEffect\": ()=>{}\n            })[\"ReactQueryDevtoolsPanel.useEffect\"]);\n        }\n    }[\"ReactQueryDevtoolsPanel.useEffect\"], [\n        props.onClose,\n        devtools\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ReactQueryDevtoolsPanel.useEffect\": ()=>{\n            devtools.setErrorTypes(errorTypes || []);\n        }\n    }[\"ReactQueryDevtoolsPanel.useEffect\"], [\n        errorTypes,\n        devtools\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ReactQueryDevtoolsPanel.useEffect\": ()=>{\n            if (ref.current) {\n                devtools.mount(ref.current);\n            }\n            return ({\n                \"ReactQueryDevtoolsPanel.useEffect\": ()=>{\n                    devtools.unmount();\n                }\n            })[\"ReactQueryDevtoolsPanel.useEffect\"];\n        }\n    }[\"ReactQueryDevtoolsPanel.useEffect\"], [\n        devtools\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n        style: {\n            height: \"500px\",\n            ...props.style\n        },\n        className: \"tsqd-parent-container\",\n        ref\n    });\n}\n //# sourceMappingURL=ReactQueryDevtoolsPanel.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tanstack+react-query-devto_f88dc5b4961f1480dcf94855f5092913/node_modules/@tanstack/react-query-devtools/build/modern/ReactQueryDevtoolsPanel.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@tanstack+react-query-devto_f88dc5b4961f1480dcf94855f5092913/node_modules/@tanstack/react-query-devtools/build/modern/index.js":
/*!***********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tanstack+react-query-devto_f88dc5b4961f1480dcf94855f5092913/node_modules/@tanstack/react-query-devtools/build/modern/index.js ***!
  \***********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReactQueryDevtools: () => (/* binding */ ReactQueryDevtools2),\n/* harmony export */   ReactQueryDevtoolsPanel: () => (/* binding */ ReactQueryDevtoolsPanel2)\n/* harmony export */ });\n/* harmony import */ var _ReactQueryDevtools_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ReactQueryDevtools.js */ \"(ssr)/./node_modules/.pnpm/@tanstack+react-query-devto_f88dc5b4961f1480dcf94855f5092913/node_modules/@tanstack/react-query-devtools/build/modern/ReactQueryDevtools.js\");\n/* harmony import */ var _ReactQueryDevtoolsPanel_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ReactQueryDevtoolsPanel.js */ \"(ssr)/./node_modules/.pnpm/@tanstack+react-query-devto_f88dc5b4961f1480dcf94855f5092913/node_modules/@tanstack/react-query-devtools/build/modern/ReactQueryDevtoolsPanel.js\");\n/* __next_internal_client_entry_do_not_use__ ReactQueryDevtools,ReactQueryDevtoolsPanel auto */ // src/index.ts\n\n\nvar ReactQueryDevtools2 =  false ? 0 : _ReactQueryDevtools_js__WEBPACK_IMPORTED_MODULE_0__.ReactQueryDevtools;\nvar ReactQueryDevtoolsPanel2 =  false ? 0 : _ReactQueryDevtoolsPanel_js__WEBPACK_IMPORTED_MODULE_1__.ReactQueryDevtoolsPanel;\n //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHRhbnN0YWNrK3JlYWN0LXF1ZXJ5LWRldnRvX2Y4OGRjNWI0OTYxZjE0ODBkY2Y5NDg1NWY1MDkyOTEzL25vZGVfbW9kdWxlcy9AdGFuc3RhY2svcmVhY3QtcXVlcnktZGV2dG9vbHMvYnVpbGQvbW9kZXJuL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBRTBCO0FBQ0s7QUFFeEIsSUFBTUEsc0JBQ1gsTUFBeUIsR0FDckIsQ0FFQyxHQUNRO0FBRVIsSUFBTUMsMkJBQ1gsTUFBeUIsR0FDckIsQ0FFQyxHQUNhIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHBjXFxEZXNrdG9wXFxQcm9qZWN0c1xcc3JjXFxpbmRleC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0ICogYXMgRGV2dG9vbHMgZnJvbSAnLi9SZWFjdFF1ZXJ5RGV2dG9vbHMnXG5pbXBvcnQgKiBhcyBEZXZ0b29sc1BhbmVsIGZyb20gJy4vUmVhY3RRdWVyeURldnRvb2xzUGFuZWwnXG5cbmV4cG9ydCBjb25zdCBSZWFjdFF1ZXJ5RGV2dG9vbHM6ICh0eXBlb2YgRGV2dG9vbHMpWydSZWFjdFF1ZXJ5RGV2dG9vbHMnXSA9XG4gIHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAnZGV2ZWxvcG1lbnQnXG4gICAgPyBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybiBudWxsXG4gICAgICB9XG4gICAgOiBEZXZ0b29scy5SZWFjdFF1ZXJ5RGV2dG9vbHNcblxuZXhwb3J0IGNvbnN0IFJlYWN0UXVlcnlEZXZ0b29sc1BhbmVsOiAodHlwZW9mIERldnRvb2xzUGFuZWwpWydSZWFjdFF1ZXJ5RGV2dG9vbHNQYW5lbCddID1cbiAgcHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdkZXZlbG9wbWVudCdcbiAgICA/IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgcmV0dXJuIG51bGxcbiAgICAgIH1cbiAgICA6IERldnRvb2xzUGFuZWwuUmVhY3RRdWVyeURldnRvb2xzUGFuZWxcbiJdLCJuYW1lcyI6WyJSZWFjdFF1ZXJ5RGV2dG9vbHMiLCJSZWFjdFF1ZXJ5RGV2dG9vbHNQYW5lbCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tanstack+react-query-devto_f88dc5b4961f1480dcf94855f5092913/node_modules/@tanstack/react-query-devtools/build/modern/index.js\n");

/***/ })

};
;