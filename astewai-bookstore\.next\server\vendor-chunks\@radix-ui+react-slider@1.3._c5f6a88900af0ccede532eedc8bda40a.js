"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-slider@1.3._c5f6a88900af0ccede532eedc8bda40a";
exports.ids = ["vendor-chunks/@radix-ui+react-slider@1.3._c5f6a88900af0ccede532eedc8bda40a"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-slider@1.3._c5f6a88900af0ccede532eedc8bda40a/node_modules/@radix-ui/react-slider/dist/index.mjs":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-slider@1.3._c5f6a88900af0ccede532eedc8bda40a/node_modules/@radix-ui/react-slider/dist/index.mjs ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Range: () => (/* binding */ Range),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   Slider: () => (/* binding */ Slider),\n/* harmony export */   SliderRange: () => (/* binding */ SliderRange),\n/* harmony export */   SliderThumb: () => (/* binding */ SliderThumb),\n/* harmony export */   SliderTrack: () => (/* binding */ SliderTrack),\n/* harmony export */   Thumb: () => (/* binding */ Thumb),\n/* harmony export */   Track: () => (/* binding */ Track),\n/* harmony export */   createSliderScope: () => (/* binding */ createSliderScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.4.4_@babel+core@7.2_0b8959437c64d880cd310af1c435e779/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_number__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/number */ \"(ssr)/./node_modules/.pnpm/@radix-ui+number@1.1.1/node_modules/@radix-ui/number/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.2/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_9f08440bbab3ef806add91f73ce9eac4/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_ad42a61e498c34b6ab0064ec44eba795/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-control_c699384c7778101ecedcd597aadb895d/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-direction@1_4c80bbbde3cb70fae665cd9492fb5af8/node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-use-previous */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-previou_d028f83ba3caad59e7d80044663957cf/node_modules/@radix-ui/react-use-previous/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-use-size */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-size@1._553827f95b2fad809b215ad51ce61834/node_modules/@radix-ui/react-use-size/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_6e0f845fa0b5165e723599b67dc13bbf/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-collection@_b26c6d948d533107753195e05bbf9d47/node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.4_@babel+core@7.2_0b8959437c64d880cd310af1c435e779/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Range,Root,Slider,SliderRange,SliderThumb,SliderTrack,Thumb,Track,createSliderScope auto */ // src/slider.tsx\n\n\n\n\n\n\n\n\n\n\n\n\nvar PAGE_KEYS = [\n    \"PageUp\",\n    \"PageDown\"\n];\nvar ARROW_KEYS = [\n    \"ArrowUp\",\n    \"ArrowDown\",\n    \"ArrowLeft\",\n    \"ArrowRight\"\n];\nvar BACK_KEYS = {\n    \"from-left\": [\n        \"Home\",\n        \"PageDown\",\n        \"ArrowDown\",\n        \"ArrowLeft\"\n    ],\n    \"from-right\": [\n        \"Home\",\n        \"PageDown\",\n        \"ArrowDown\",\n        \"ArrowRight\"\n    ],\n    \"from-bottom\": [\n        \"Home\",\n        \"PageDown\",\n        \"ArrowDown\",\n        \"ArrowLeft\"\n    ],\n    \"from-top\": [\n        \"Home\",\n        \"PageDown\",\n        \"ArrowUp\",\n        \"ArrowLeft\"\n    ]\n};\nvar SLIDER_NAME = \"Slider\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_2__.createCollection)(SLIDER_NAME);\nvar [createSliderContext, createSliderScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_3__.createContextScope)(SLIDER_NAME, [\n    createCollectionScope\n]);\nvar [SliderProvider, useSliderContext] = createSliderContext(SLIDER_NAME);\nvar Slider = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { name, min = 0, max = 100, step = 1, orientation = \"horizontal\", disabled = false, minStepsBetweenThumbs = 0, defaultValue = [\n        min\n    ], value, onValueChange = ()=>{}, onValueCommit = ()=>{}, inverted = false, form, ...sliderProps } = props;\n    const thumbRefs = react__WEBPACK_IMPORTED_MODULE_0__.useRef(/* @__PURE__ */ new Set());\n    const valueIndexToChangeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const isHorizontal = orientation === \"horizontal\";\n    const SliderOrientation = isHorizontal ? SliderHorizontal : SliderVertical;\n    const [values = [], setValues] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__.useControllableState)({\n        prop: value,\n        defaultProp: defaultValue,\n        onChange: {\n            \"Slider.useControllableState\": (value2)=>{\n                const thumbs = [\n                    ...thumbRefs.current\n                ];\n                thumbs[valueIndexToChangeRef.current]?.focus();\n                onValueChange(value2);\n            }\n        }[\"Slider.useControllableState\"]\n    });\n    const valuesBeforeSlideStartRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(values);\n    function handleSlideStart(value2) {\n        const closestIndex = getClosestValueIndex(values, value2);\n        updateValues(value2, closestIndex);\n    }\n    function handleSlideMove(value2) {\n        updateValues(value2, valueIndexToChangeRef.current);\n    }\n    function handleSlideEnd() {\n        const prevValue = valuesBeforeSlideStartRef.current[valueIndexToChangeRef.current];\n        const nextValue = values[valueIndexToChangeRef.current];\n        const hasChanged = nextValue !== prevValue;\n        if (hasChanged) onValueCommit(values);\n    }\n    function updateValues(value2, atIndex, { commit } = {\n        commit: false\n    }) {\n        const decimalCount = getDecimalCount(step);\n        const snapToStep = roundValue(Math.round((value2 - min) / step) * step + min, decimalCount);\n        const nextValue = (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_5__.clamp)(snapToStep, [\n            min,\n            max\n        ]);\n        setValues((prevValues = [])=>{\n            const nextValues = getNextSortedValues(prevValues, nextValue, atIndex);\n            if (hasMinStepsBetweenValues(nextValues, minStepsBetweenThumbs * step)) {\n                valueIndexToChangeRef.current = nextValues.indexOf(nextValue);\n                const hasChanged = String(nextValues) !== String(prevValues);\n                if (hasChanged && commit) onValueCommit(nextValues);\n                return hasChanged ? nextValues : prevValues;\n            } else {\n                return prevValues;\n            }\n        });\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SliderProvider, {\n        scope: props.__scopeSlider,\n        name,\n        disabled,\n        min,\n        max,\n        valueIndexToChangeRef,\n        thumbs: thumbRefs.current,\n        values,\n        orientation,\n        form,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Provider, {\n            scope: props.__scopeSlider,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Slot, {\n                scope: props.__scopeSlider,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SliderOrientation, {\n                    \"aria-disabled\": disabled,\n                    \"data-disabled\": disabled ? \"\" : void 0,\n                    ...sliderProps,\n                    ref: forwardedRef,\n                    onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(sliderProps.onPointerDown, ()=>{\n                        if (!disabled) valuesBeforeSlideStartRef.current = values;\n                    }),\n                    min,\n                    max,\n                    inverted,\n                    onSlideStart: disabled ? void 0 : handleSlideStart,\n                    onSlideMove: disabled ? void 0 : handleSlideMove,\n                    onSlideEnd: disabled ? void 0 : handleSlideEnd,\n                    onHomeKeyDown: ()=>!disabled && updateValues(min, 0, {\n                            commit: true\n                        }),\n                    onEndKeyDown: ()=>!disabled && updateValues(max, values.length - 1, {\n                            commit: true\n                        }),\n                    onStepKeyDown: ({ event, direction: stepDirection })=>{\n                        if (!disabled) {\n                            const isPageKey = PAGE_KEYS.includes(event.key);\n                            const isSkipKey = isPageKey || event.shiftKey && ARROW_KEYS.includes(event.key);\n                            const multiplier = isSkipKey ? 10 : 1;\n                            const atIndex = valueIndexToChangeRef.current;\n                            const value2 = values[atIndex];\n                            const stepInDirection = step * multiplier * stepDirection;\n                            updateValues(value2 + stepInDirection, atIndex, {\n                                commit: true\n                            });\n                        }\n                    }\n                })\n            })\n        })\n    });\n});\nSlider.displayName = SLIDER_NAME;\nvar [SliderOrientationProvider, useSliderOrientationContext] = createSliderContext(SLIDER_NAME, {\n    startEdge: \"left\",\n    endEdge: \"right\",\n    size: \"width\",\n    direction: 1\n});\nvar SliderHorizontal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { min, max, dir, inverted, onSlideStart, onSlideMove, onSlideEnd, onStepKeyDown, ...sliderProps } = props;\n    const [slider, setSlider] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_7__.useComposedRefs)(forwardedRef, {\n        \"SliderHorizontal.useComposedRefs[composedRefs]\": (node)=>setSlider(node)\n    }[\"SliderHorizontal.useComposedRefs[composedRefs]\"]);\n    const rectRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(void 0);\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_8__.useDirection)(dir);\n    const isDirectionLTR = direction === \"ltr\";\n    const isSlidingFromLeft = isDirectionLTR && !inverted || !isDirectionLTR && inverted;\n    function getValueFromPointer(pointerPosition) {\n        const rect = rectRef.current || slider.getBoundingClientRect();\n        const input = [\n            0,\n            rect.width\n        ];\n        const output = isSlidingFromLeft ? [\n            min,\n            max\n        ] : [\n            max,\n            min\n        ];\n        const value = linearScale(input, output);\n        rectRef.current = rect;\n        return value(pointerPosition - rect.left);\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SliderOrientationProvider, {\n        scope: props.__scopeSlider,\n        startEdge: isSlidingFromLeft ? \"left\" : \"right\",\n        endEdge: isSlidingFromLeft ? \"right\" : \"left\",\n        direction: isSlidingFromLeft ? 1 : -1,\n        size: \"width\",\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SliderImpl, {\n            dir: direction,\n            \"data-orientation\": \"horizontal\",\n            ...sliderProps,\n            ref: composedRefs,\n            style: {\n                ...sliderProps.style,\n                [\"--radix-slider-thumb-transform\"]: \"translateX(-50%)\"\n            },\n            onSlideStart: (event)=>{\n                const value = getValueFromPointer(event.clientX);\n                onSlideStart?.(value);\n            },\n            onSlideMove: (event)=>{\n                const value = getValueFromPointer(event.clientX);\n                onSlideMove?.(value);\n            },\n            onSlideEnd: ()=>{\n                rectRef.current = void 0;\n                onSlideEnd?.();\n            },\n            onStepKeyDown: (event)=>{\n                const slideDirection = isSlidingFromLeft ? \"from-left\" : \"from-right\";\n                const isBackKey = BACK_KEYS[slideDirection].includes(event.key);\n                onStepKeyDown?.({\n                    event,\n                    direction: isBackKey ? -1 : 1\n                });\n            }\n        })\n    });\n});\nvar SliderVertical = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { min, max, inverted, onSlideStart, onSlideMove, onSlideEnd, onStepKeyDown, ...sliderProps } = props;\n    const sliderRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const ref = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_7__.useComposedRefs)(forwardedRef, sliderRef);\n    const rectRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(void 0);\n    const isSlidingFromBottom = !inverted;\n    function getValueFromPointer(pointerPosition) {\n        const rect = rectRef.current || sliderRef.current.getBoundingClientRect();\n        const input = [\n            0,\n            rect.height\n        ];\n        const output = isSlidingFromBottom ? [\n            max,\n            min\n        ] : [\n            min,\n            max\n        ];\n        const value = linearScale(input, output);\n        rectRef.current = rect;\n        return value(pointerPosition - rect.top);\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SliderOrientationProvider, {\n        scope: props.__scopeSlider,\n        startEdge: isSlidingFromBottom ? \"bottom\" : \"top\",\n        endEdge: isSlidingFromBottom ? \"top\" : \"bottom\",\n        size: \"height\",\n        direction: isSlidingFromBottom ? 1 : -1,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SliderImpl, {\n            \"data-orientation\": \"vertical\",\n            ...sliderProps,\n            ref,\n            style: {\n                ...sliderProps.style,\n                [\"--radix-slider-thumb-transform\"]: \"translateY(50%)\"\n            },\n            onSlideStart: (event)=>{\n                const value = getValueFromPointer(event.clientY);\n                onSlideStart?.(value);\n            },\n            onSlideMove: (event)=>{\n                const value = getValueFromPointer(event.clientY);\n                onSlideMove?.(value);\n            },\n            onSlideEnd: ()=>{\n                rectRef.current = void 0;\n                onSlideEnd?.();\n            },\n            onStepKeyDown: (event)=>{\n                const slideDirection = isSlidingFromBottom ? \"from-bottom\" : \"from-top\";\n                const isBackKey = BACK_KEYS[slideDirection].includes(event.key);\n                onStepKeyDown?.({\n                    event,\n                    direction: isBackKey ? -1 : 1\n                });\n            }\n        })\n    });\n});\nvar SliderImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSlider, onSlideStart, onSlideMove, onSlideEnd, onHomeKeyDown, onEndKeyDown, onStepKeyDown, ...sliderProps } = props;\n    const context = useSliderContext(SLIDER_NAME, __scopeSlider);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_9__.Primitive.span, {\n        ...sliderProps,\n        ref: forwardedRef,\n        onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(props.onKeyDown, (event)=>{\n            if (event.key === \"Home\") {\n                onHomeKeyDown(event);\n                event.preventDefault();\n            } else if (event.key === \"End\") {\n                onEndKeyDown(event);\n                event.preventDefault();\n            } else if (PAGE_KEYS.concat(ARROW_KEYS).includes(event.key)) {\n                onStepKeyDown(event);\n                event.preventDefault();\n            }\n        }),\n        onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(props.onPointerDown, (event)=>{\n            const target = event.target;\n            target.setPointerCapture(event.pointerId);\n            event.preventDefault();\n            if (context.thumbs.has(target)) {\n                target.focus();\n            } else {\n                onSlideStart(event);\n            }\n        }),\n        onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(props.onPointerMove, (event)=>{\n            const target = event.target;\n            if (target.hasPointerCapture(event.pointerId)) onSlideMove(event);\n        }),\n        onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(props.onPointerUp, (event)=>{\n            const target = event.target;\n            if (target.hasPointerCapture(event.pointerId)) {\n                target.releasePointerCapture(event.pointerId);\n                onSlideEnd(event);\n            }\n        })\n    });\n});\nvar TRACK_NAME = \"SliderTrack\";\nvar SliderTrack = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSlider, ...trackProps } = props;\n    const context = useSliderContext(TRACK_NAME, __scopeSlider);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_9__.Primitive.span, {\n        \"data-disabled\": context.disabled ? \"\" : void 0,\n        \"data-orientation\": context.orientation,\n        ...trackProps,\n        ref: forwardedRef\n    });\n});\nSliderTrack.displayName = TRACK_NAME;\nvar RANGE_NAME = \"SliderRange\";\nvar SliderRange = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSlider, ...rangeProps } = props;\n    const context = useSliderContext(RANGE_NAME, __scopeSlider);\n    const orientation = useSliderOrientationContext(RANGE_NAME, __scopeSlider);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_7__.useComposedRefs)(forwardedRef, ref);\n    const valuesCount = context.values.length;\n    const percentages = context.values.map((value)=>convertValueToPercentage(value, context.min, context.max));\n    const offsetStart = valuesCount > 1 ? Math.min(...percentages) : 0;\n    const offsetEnd = 100 - Math.max(...percentages);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_9__.Primitive.span, {\n        \"data-orientation\": context.orientation,\n        \"data-disabled\": context.disabled ? \"\" : void 0,\n        ...rangeProps,\n        ref: composedRefs,\n        style: {\n            ...props.style,\n            [orientation.startEdge]: offsetStart + \"%\",\n            [orientation.endEdge]: offsetEnd + \"%\"\n        }\n    });\n});\nSliderRange.displayName = RANGE_NAME;\nvar THUMB_NAME = \"SliderThumb\";\nvar SliderThumb = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const getItems = useCollection(props.__scopeSlider);\n    const [thumb, setThumb] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_7__.useComposedRefs)(forwardedRef, {\n        \"SliderThumb.useComposedRefs[composedRefs]\": (node)=>setThumb(node)\n    }[\"SliderThumb.useComposedRefs[composedRefs]\"]);\n    const index = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"SliderThumb.useMemo[index]\": ()=>thumb ? getItems().findIndex({\n                \"SliderThumb.useMemo[index]\": (item)=>item.ref.current === thumb\n            }[\"SliderThumb.useMemo[index]\"]) : -1\n    }[\"SliderThumb.useMemo[index]\"], [\n        getItems,\n        thumb\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SliderThumbImpl, {\n        ...props,\n        ref: composedRefs,\n        index\n    });\n});\nvar SliderThumbImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSlider, index, name, ...thumbProps } = props;\n    const context = useSliderContext(THUMB_NAME, __scopeSlider);\n    const orientation = useSliderOrientationContext(THUMB_NAME, __scopeSlider);\n    const [thumb, setThumb] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_7__.useComposedRefs)(forwardedRef, {\n        \"SliderThumbImpl.useComposedRefs[composedRefs]\": (node)=>setThumb(node)\n    }[\"SliderThumbImpl.useComposedRefs[composedRefs]\"]);\n    const isFormControl = thumb ? context.form || !!thumb.closest(\"form\") : true;\n    const size = (0,_radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_10__.useSize)(thumb);\n    const value = context.values[index];\n    const percent = value === void 0 ? 0 : convertValueToPercentage(value, context.min, context.max);\n    const label = getLabel(index, context.values.length);\n    const orientationSize = size?.[orientation.size];\n    const thumbInBoundsOffset = orientationSize ? getThumbInBoundsOffset(orientationSize, percent, orientation.direction) : 0;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"SliderThumbImpl.useEffect\": ()=>{\n            if (thumb) {\n                context.thumbs.add(thumb);\n                return ({\n                    \"SliderThumbImpl.useEffect\": ()=>{\n                        context.thumbs.delete(thumb);\n                    }\n                })[\"SliderThumbImpl.useEffect\"];\n            }\n        }\n    }[\"SliderThumbImpl.useEffect\"], [\n        thumb,\n        context.thumbs\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(\"span\", {\n        style: {\n            transform: \"var(--radix-slider-thumb-transform)\",\n            position: \"absolute\",\n            [orientation.startEdge]: `calc(${percent}% + ${thumbInBoundsOffset}px)`\n        },\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.ItemSlot, {\n                scope: props.__scopeSlider,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_9__.Primitive.span, {\n                    role: \"slider\",\n                    \"aria-label\": props[\"aria-label\"] || label,\n                    \"aria-valuemin\": context.min,\n                    \"aria-valuenow\": value,\n                    \"aria-valuemax\": context.max,\n                    \"aria-orientation\": context.orientation,\n                    \"data-orientation\": context.orientation,\n                    \"data-disabled\": context.disabled ? \"\" : void 0,\n                    tabIndex: context.disabled ? void 0 : 0,\n                    ...thumbProps,\n                    ref: composedRefs,\n                    style: value === void 0 ? {\n                        display: \"none\"\n                    } : props.style,\n                    onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(props.onFocus, ()=>{\n                        context.valueIndexToChangeRef.current = index;\n                    })\n                })\n            }),\n            isFormControl && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SliderBubbleInput, {\n                name: name ?? (context.name ? context.name + (context.values.length > 1 ? \"[]\" : \"\") : void 0),\n                form: context.form,\n                value\n            }, index)\n        ]\n    });\n});\nSliderThumb.displayName = THUMB_NAME;\nvar BUBBLE_INPUT_NAME = \"RadioBubbleInput\";\nvar SliderBubbleInput = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(({ __scopeSlider, value, ...props }, forwardedRef)=>{\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_7__.useComposedRefs)(ref, forwardedRef);\n    const prevValue = (0,_radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_11__.usePrevious)(value);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"SliderBubbleInput.useEffect\": ()=>{\n            const input = ref.current;\n            if (!input) return;\n            const inputProto = window.HTMLInputElement.prototype;\n            const descriptor = Object.getOwnPropertyDescriptor(inputProto, \"value\");\n            const setValue = descriptor.set;\n            if (prevValue !== value && setValue) {\n                const event = new Event(\"input\", {\n                    bubbles: true\n                });\n                setValue.call(input, value);\n                input.dispatchEvent(event);\n            }\n        }\n    }[\"SliderBubbleInput.useEffect\"], [\n        prevValue,\n        value\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_9__.Primitive.input, {\n        style: {\n            display: \"none\"\n        },\n        ...props,\n        ref: composedRefs,\n        defaultValue: value\n    });\n});\nSliderBubbleInput.displayName = BUBBLE_INPUT_NAME;\nfunction getNextSortedValues(prevValues = [], nextValue, atIndex) {\n    const nextValues = [\n        ...prevValues\n    ];\n    nextValues[atIndex] = nextValue;\n    return nextValues.sort((a, b)=>a - b);\n}\nfunction convertValueToPercentage(value, min, max) {\n    const maxSteps = max - min;\n    const percentPerStep = 100 / maxSteps;\n    const percentage = percentPerStep * (value - min);\n    return (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_5__.clamp)(percentage, [\n        0,\n        100\n    ]);\n}\nfunction getLabel(index, totalValues) {\n    if (totalValues > 2) {\n        return `Value ${index + 1} of ${totalValues}`;\n    } else if (totalValues === 2) {\n        return [\n            \"Minimum\",\n            \"Maximum\"\n        ][index];\n    } else {\n        return void 0;\n    }\n}\nfunction getClosestValueIndex(values, nextValue) {\n    if (values.length === 1) return 0;\n    const distances = values.map((value)=>Math.abs(value - nextValue));\n    const closestDistance = Math.min(...distances);\n    return distances.indexOf(closestDistance);\n}\nfunction getThumbInBoundsOffset(width, left, direction) {\n    const halfWidth = width / 2;\n    const halfPercent = 50;\n    const offset = linearScale([\n        0,\n        halfPercent\n    ], [\n        0,\n        halfWidth\n    ]);\n    return (halfWidth - offset(left) * direction) * direction;\n}\nfunction getStepsBetweenValues(values) {\n    return values.slice(0, -1).map((value, index)=>values[index + 1] - value);\n}\nfunction hasMinStepsBetweenValues(values, minStepsBetweenValues) {\n    if (minStepsBetweenValues > 0) {\n        const stepsBetweenValues = getStepsBetweenValues(values);\n        const actualMinStepsBetweenValues = Math.min(...stepsBetweenValues);\n        return actualMinStepsBetweenValues >= minStepsBetweenValues;\n    }\n    return true;\n}\nfunction linearScale(input, output) {\n    return (value)=>{\n        if (input[0] === input[1] || output[0] === output[1]) return output[0];\n        const ratio = (output[1] - output[0]) / (input[1] - input[0]);\n        return output[0] + ratio * (value - input[0]);\n    };\n}\nfunction getDecimalCount(value) {\n    return (String(value).split(\".\")[1] || \"\").length;\n}\nfunction roundValue(value, decimalCount) {\n    const rounder = Math.pow(10, decimalCount);\n    return Math.round(value * rounder) / rounder;\n}\nvar Root = Slider;\nvar Track = SliderTrack;\nvar Range = SliderRange;\nvar Thumb = SliderThumb;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-slider@1.3._c5f6a88900af0ccede532eedc8bda40a/node_modules/@radix-ui/react-slider/dist/index.mjs\n");

/***/ })

};
;