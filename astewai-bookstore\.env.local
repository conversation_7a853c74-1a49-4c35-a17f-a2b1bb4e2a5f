# =============================================
# ASTEWAI DIGITAL BOOKSTORE - ENVIRONMENT VARIABLES
# =============================================
# This file contains all environment variables needed for the application.
# Update the values below with your actual credentials for fresh Supabase setup.

# =============================================
# SUPABASE CONFIGURATION (REQUIRED)
# =============================================
# Update these with your NEW Supabase project credentials
NEXT_PUBLIC_SUPABASE_URL=https://prideiiqovcbmwjkntrq.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InByaWRlaWlxb3ZjYm13amtudHJxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTgyNjM3NTIsImV4cCI6MjA3MzgzOTc1Mn0.d_V2Da13m-Nm13ecXVy1i6cf82g1FwofKuE7Rj29e0A
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InByaWRlaWlxb3ZjYm13amtudHJxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1ODI2Mzc1MiwiZXhwIjoyMDczODM5NzUyfQ.3yDK2vaF8Dwux37UGeqqEkJKVYnP0bFmF9EbKJ1XZps
# =============================================
# SITE CONFIGURATION (REQUIRED)
# =============================================
# Critical for email confirmation and authentication flows
NEXT_PUBLIC_SITE_URL=https://astewai-bookstore-84cc1ea5f-getachewbinyam5-gmailcoms-projects.vercel.app
SUPABASE_AUTH_SITE_URL=https://astewai-bookstore-84cc1ea5f-getachewbinyam5-gmailcoms-projects.vercel.app

# =============================================
# PAYMENT CONFIGURATION (REQUIRED)
# =============================================
# Chapa payment gateway for Ethiopian market
CHAPA_SECRET_KEY=CHAPUBK_TEST-QcdliLnsHljvGKPpWGgadiL7JPuiaikX

# =============================================
# EMAIL CONFIGURATION (OPTIONAL)
# =============================================
# Uncomment and configure for production email features
# RESEND_API_KEY=your_resend_api_key_here
# ADMIN_EMAIL=<EMAIL>
# SUPPORT_EMAIL=<EMAIL>

# =============================================
# ANALYTICS CONFIGURATION (OPTIONAL)
# =============================================
# Plausible Analytics for privacy-friendly tracking
# NEXT_PUBLIC_PLAUSIBLE_DOMAIN=astewai-bookstore.vercel.app
# NEXT_PUBLIC_PLAUSIBLE_API_HOST=https://plausible.io
STORE_ANALYTICS_EVENTS=false

# =============================================
# SEO CONFIGURATION (OPTIONAL)
# =============================================
# Search engine verification codes
# GOOGLE_SITE_VERIFICATION=your_google_verification_code
# YANDEX_VERIFICATION=your_yandex_verification_code
# YAHOO_VERIFICATION=your_yahoo_verification_code

# =============================================
# TELEGRAM BOT CONFIGURATION (OPTIONAL)
# =============================================
# For admin notifications and customer support
TELEGRAM_BOT_NAME=astewai_bookstore_bot
TELEGRAM_BOT_TOKEN=1234567890:ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789
TELEGRAM_ADMIN_CHANNEL_ID=-1001234567890

# =============================================
# DEVELOPMENT CONFIGURATION
# =============================================
# Additional settings for development/debugging
# NODE_ENV=development
# DEBUG=true